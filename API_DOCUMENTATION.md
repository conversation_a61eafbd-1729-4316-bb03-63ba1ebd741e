# 房屋租赁平台 API 文档

## 概述

本文档描述了房屋租赁平台后端 API 的使用方法。所有 API 都通过统一的云函数入口 `api-router` 提供服务。

## 基础信息

- **基础URL**: `https://your-cloud-function-url/api-router`
- **请求格式**: JSON
- **响应格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

```json
{
  "code": 0,
  "message": "成功",
  "data": {}
}
```

- `code`: 状态码，0表示成功，非0表示失败
- `message`: 响应消息
- `data`: 响应数据

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权/认证失败 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |

## 认证机制

需要认证的接口需要在请求中包含 `token` 参数：

```json
{
  "token": "your_access_token",
  "other_params": "..."
}
```

## API 接口

### 1. 用户管理

#### 1.1 用户注册

**接口**: `POST /user/register`

**请求参数**:
```json
{
  "username": "用户名",
  "password": "密码",
  "nickname": "昵称（可选）",
  "phone": "手机号（可选）",
  "email": "邮箱（可选）"
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "注册成功",
  "data": {
    "uid": "用户ID",
    "username": "用户名",
    "nickname": "昵称"
  }
}
```

#### 1.2 用户登录

**接口**: `POST /user/login`

**请求参数**:
```json
{
  "username": "用户名",
  "password": "密码"
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "登录成功",
  "data": {
    "token": "访问令牌",
    "uid": "用户ID",
    "username": "用户名",
    "nickname": "昵称",
    "avatar": "头像URL",
    "role": ["user"]
  }
}
```

#### 1.3 用户登出

**接口**: `POST /user/logout`

**需要认证**: 是

**请求参数**:
```json
{
  "token": "访问令牌"
}
```

#### 1.4 获取用户信息

**接口**: `GET /user/profile`

**需要认证**: 是

**响应示例**:
```json
{
  "code": 0,
  "message": "获取成功",
  "data": {
    "_id": "用户ID",
    "username": "用户名",
    "nickname": "昵称",
    "avatar": "头像URL",
    "phone": "手机号",
    "email": "邮箱",
    "created_at": "注册时间"
  }
}
```

#### 1.5 更新用户信息

**接口**: `PUT /user/profile`

**需要认证**: 是

**请求参数**:
```json
{
  "token": "访问令牌",
  "nickname": "新昵称（可选）",
  "avatar": "新头像URL（可选）",
  "phone": "新手机号（可选）",
  "email": "新邮箱（可选）"
}
```

#### 1.6 修改密码

**接口**: `PUT /user/password`

**需要认证**: 是

**请求参数**:
```json
{
  "token": "访问令牌",
  "oldPassword": "旧密码",
  "newPassword": "新密码"
}
```

### 2. 房源管理

#### 2.1 获取房源列表

**接口**: `GET /house/list`

**请求参数**:
```json
{
  "page": 1,
  "limit": 10,
  "status": "approved",
  "district": "区域（可选）",
  "type": "房型（可选）",
  "minPrice": "最低价格（可选）",
  "maxPrice": "最高价格（可选）",
  "config": ["配置数组（可选）"],
  "sortBy": "排序字段（可选）",
  "sortOrder": "排序方向（可选）"
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "_id": "房源ID",
        "title": "房源标题",
        "desc": "房源描述",
        "images": ["图片URL数组"],
        "location": {
          "address": "详细地址",
          "district": "区域"
        },
        "price": 3000,
        "type": "一居",
        "config": ["空调", "洗衣机"],
        "created_at": "发布时间"
      }
    ],
    "total": 100,
    "page": 1,
    "limit": 10,
    "totalPages": 10
  }
}
```

#### 2.2 获取房源详情

**接口**: `GET /house/detail/:id`

**响应示例**:
```json
{
  "code": 0,
  "message": "获取成功",
  "data": {
    "_id": "房源ID",
    "title": "房源标题",
    "desc": "房源描述",
    "images": ["图片URL数组"],
    "location": {
      "address": "详细地址",
      "latitude": 39.9042,
      "longitude": 116.4074,
      "district": "区域",
      "subway": "地铁站"
    },
    "price": 3000,
    "type": "一居",
    "config": ["空调", "洗衣机"],
    "contact": {
      "phone": "联系电话",
      "wechat": "微信号"
    },
    "area": 50,
    "floor": "10/20",
    "orientation": "南",
    "is_furnished": true,
    "view_count": 100,
    "favorite_count": 10,
    "owner_info": {
      "nickname": "房东昵称",
      "avatar": "房东头像"
    },
    "created_at": "发布时间"
  }
}
```

#### 2.3 发布房源

**接口**: `POST /house/create`

**需要认证**: 是

**请求参数**:
```json
{
  "token": "访问令牌",
  "title": "房源标题",
  "desc": "房源描述",
  "images": ["图片URL数组"],
  "location": {
    "address": "详细地址",
    "latitude": 39.9042,
    "longitude": 116.4074,
    "district": "区域",
    "subway": "地铁站"
  },
  "price": 3000,
  "type": "一居",
  "config": ["空调", "洗衣机"],
  "contact": {
    "phone": "联系电话",
    "wechat": "微信号"
  },
  "area": 50,
  "floor": "10/20",
  "orientation": "南",
  "is_furnished": true
}
```

#### 2.4 搜索房源

**接口**: `GET /house/search`

**请求参数**:
```json
{
  "keyword": "搜索关键词",
  "page": 1,
  "limit": 10,
  "district": "区域（可选）",
  "type": "房型（可选）",
  "minPrice": "最低价格（可选）",
  "maxPrice": "最高价格（可选）",
  "config": ["配置数组（可选）"]
}
```

### 3. 收藏管理

#### 3.1 添加收藏

**接口**: `POST /favorite/add`

**需要认证**: 是

**请求参数**:
```json
{
  "token": "访问令牌",
  "house_id": "房源ID"
}
```

#### 3.2 取消收藏

**接口**: `DELETE /favorite/remove`

**需要认证**: 是

**请求参数**:
```json
{
  "token": "访问令牌",
  "house_id": "房源ID"
}
```

#### 3.3 获取收藏列表

**接口**: `GET /favorite/list`

**需要认证**: 是

**请求参数**:
```json
{
  "token": "访问令牌",
  "page": 1,
  "limit": 10
}
```

### 4. 文件上传

#### 4.1 上传图片

**接口**: `POST /upload/image`

**需要认证**: 是

**请求参数**:
```json
{
  "token": "访问令牌",
  "file": "base64编码的图片数据",
  "fileName": "文件名",
  "fileType": "文件类型"
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "上传成功",
  "data": {
    "fileID": "云存储文件ID",
    "fileURL": "文件访问URL",
    "cloudPath": "云存储路径"
  }
}
```

### 5. 管理员接口

#### 5.1 获取用户列表

**接口**: `GET /admin/users`

**需要认证**: 是（管理员）

#### 5.2 封禁/解封用户

**接口**: `PUT /admin/user/ban/:id` / `PUT /admin/user/unban/:id`

**需要认证**: 是（管理员）

#### 5.3 房源审核

**接口**: `PUT /admin/house/approve/:id` / `PUT /admin/house/reject/:id`

**需要认证**: 是（管理员）

#### 5.4 获取统计数据

**接口**: `GET /admin/statistics`

**需要认证**: 是（管理员）

## 安全特性

1. **防刷机制**: 限制每分钟最多60次请求
2. **输入验证**: 防止XSS攻击和SQL注入
3. **文件上传安全**: 限制文件类型和大小
4. **用户状态检查**: 自动检查用户是否被封禁
5. **数据脱敏**: 敏感信息自动脱敏处理

## 使用示例

### JavaScript 示例

```javascript
// 用户登录
const loginResult = await uniCloud.callFunction({
  name: 'api-router',
  data: {
    httpMethod: 'POST',
    path: '/user/login',
    body: JSON.stringify({
      username: 'testuser',
      password: '123456'
    })
  }
});

// 获取房源列表
const houseListResult = await uniCloud.callFunction({
  name: 'api-router',
  data: {
    httpMethod: 'GET',
    path: '/house/list',
    queryStringParameters: {
      page: 1,
      limit: 10,
      status: 'approved'
    }
  }
});
```

## 注意事项

1. 所有时间字段均为 ISO 8601 格式
2. 图片上传支持 JPEG、PNG、GIF、WebP 格式，最大5MB
3. 房源发布后需要管理员审核才能显示
4. 用户被封禁后无法进行任何操作
5. API 调用频率限制为每分钟60次
