# 房屋租赁平台后端部署指南

## 概述

本指南将帮助您部署房屋租赁平台的后端服务，基于 uniCloud 云开发平台。

## 前置要求

1. **HBuilderX**: 版本 3.0 或以上
2. **uniCloud 账号**: 已注册并创建云服务空间
3. **Node.js**: 版本 14 或以上（用于本地开发）

## 部署步骤

### 1. 项目配置

#### 1.1 关联云服务空间

1. 在 HBuilderX 中打开项目
2. 右键点击 `uniCloud-aliyun` 目录
3. 选择 "关联云服务空间"
4. 选择您的阿里云服务空间

#### 1.2 配置 uni-id

1. 修改 `uniCloud-aliyun/cloudfunctions/common/uni-config-center/uni-id/config.json`
2. 更新以下配置项：

```json
{
  "passwordSecret": "your_password_secret_here",
  "tokenSecret": "your_token_secret_here",
  "tokenExpiresIn": 7200,
  "passwordErrorLimit": 6,
  "passwordErrorRetryTime": 3600
}
```

**重要**: 请将 `passwordSecret` 和 `tokenSecret` 替换为您自己的安全密钥。

### 2. 数据库初始化

#### 2.1 创建数据库集合

1. 在 HBuilderX 中打开 `uniCloud-aliyun/database/` 目录
2. 右键点击每个 `.schema.json` 文件
3. 选择 "上传 DB Schema"

需要上传的 Schema 文件：
- `house.schema.json` - 房源集合
- `user.schema.json` - 用户集合
- `user_favorites.schema.json` - 用户收藏集合
- `system_config.schema.json` - 系统配置集合
- `upload_files.schema.json` - 上传文件记录集合
- `api_requests.schema.json` - API 请求记录集合

#### 2.2 创建数据库索引

1. 打开 uniCloud Web 控制台
2. 进入数据库管理
3. 根据 `indexes.json` 文件创建相应的索引

### 3. 云函数部署

#### 3.1 安装依赖

1. 右键点击 `uniCloud-aliyun/cloudfunctions/api-router` 目录
2. 选择 "在终端中打开"
3. 运行命令：

```bash
npm install
```

#### 3.2 上传云函数

1. 右键点击 `api-router` 云函数目录
2. 选择 "上传并运行"
3. 等待上传完成

#### 3.3 配置云函数

1. 在 uniCloud Web 控制台中找到 `api-router` 云函数
2. 配置以下环境变量（如需要）：
   - `NODE_ENV`: production
   - 其他自定义环境变量

### 4. 初始化系统数据

#### 4.1 创建管理员账号

使用 JQL 查询工具执行以下代码创建管理员账号：

```javascript
// 创建管理员用户
const adminUser = {
  username: 'admin',
  password: 'your_admin_password_here', // 请修改为安全密码
  nickname: '系统管理员',
  role: ['admin'],
  is_banned: false,
  created_at: new Date(),
  updated_at: new Date()
};

// 注意：密码需要使用 uni-id 进行加密
// 建议通过注册接口创建管理员账号，然后手动修改角色
db.collection('user').add(adminUser);
```

#### 4.2 初始化系统配置

```javascript
// 初始化系统配置
const systemConfigs = [
  {
    key: 'banner_list',
    value: [
      {
        image: 'https://example.com/banner1.jpg',
        title: '欢迎使用房屋租赁平台',
        link: ''
      }
    ],
    description: '首页轮播图配置'
  },
  {
    key: 'announcement',
    value: '欢迎使用房屋租赁平台！',
    description: '系统公告'
  },
  {
    key: 'search_keywords',
    value: ['一居室', '二居室', '整租', '合租', '地铁房'],
    description: '搜索热词'
  },
  {
    key: 'contact_info',
    value: {
      phone: '************',
      email: '<EMAIL>',
      wechat: 'service_wechat'
    },
    description: '联系方式'
  }
];

systemConfigs.forEach(config => {
  config.updated_at = new Date();
  db.collection('system_config').add(config);
});
```

### 5. 测试部署

#### 5.1 运行测试用例

1. 打开 `uniCloud-aliyun/cloudfunctions/api-router/test/api-test.js`
2. 修改测试配置中的用户信息
3. 在 HBuilderX 中运行测试文件

#### 5.2 手动测试

使用 uniCloud Web 控制台的云函数测试功能：

```json
{
  "httpMethod": "POST",
  "path": "/user/register",
  "body": "{\"username\":\"testuser\",\"password\":\"123456\",\"nickname\":\"测试用户\"}"
}
```

### 6. 安全配置

#### 6.1 配置域名白名单

1. 在 uniCloud Web 控制台中配置允许访问的域名
2. 添加您的小程序域名和管理后台域名

#### 6.2 配置 HTTPS

确保所有 API 调用都使用 HTTPS 协议。

#### 6.3 定期备份

1. 设置数据库自动备份
2. 定期下载备份文件

### 7. 监控与维护

#### 7.1 日志监控

1. 在 uniCloud Web 控制台查看云函数日志
2. 监控错误率和响应时间

#### 7.2 性能优化

1. 监控数据库查询性能
2. 优化慢查询
3. 适当增加索引

#### 7.3 定期清理

定期清理过期的 API 请求记录：

```javascript
// 清理7天前的 API 请求记录
const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
db.collection('api_requests').where({
  created_at: db.command.lt(sevenDaysAgo)
}).remove();
```

## 常见问题

### Q1: 云函数上传失败

**解决方案**:
1. 检查网络连接
2. 确认云服务空间关联正确
3. 检查云函数代码是否有语法错误

### Q2: 数据库连接失败

**解决方案**:
1. 确认数据库集合已创建
2. 检查 Schema 权限配置
3. 验证 uni-id 配置是否正确

### Q3: API 调用返回 401 错误

**解决方案**:
1. 检查 token 是否有效
2. 确认用户是否被封禁
3. 验证 uni-id 配置

### Q4: 文件上传失败

**解决方案**:
1. 检查文件大小是否超限
2. 确认文件类型是否支持
3. 验证云存储配置

## 技术支持

如果在部署过程中遇到问题，请：

1. 查看 HBuilderX 控制台错误信息
2. 检查 uniCloud Web 控制台日志
3. 参考 uniCloud 官方文档
4. 联系技术支持团队

## 版本更新

### 更新步骤

1. 备份当前数据库
2. 下载新版本代码
3. 比较配置文件差异
4. 更新云函数代码
5. 运行数据库迁移脚本（如有）
6. 测试功能完整性

### 回滚方案

如果更新后出现问题：

1. 恢复云函数到上一版本
2. 恢复数据库备份
3. 验证系统功能正常

---

**注意**: 部署前请仔细阅读本指南，确保所有配置项都已正确设置。建议先在测试环境中完成部署和测试，再部署到生产环境。
