<template>
	<view class="empty-state" :style="{ padding: padding + 'rpx' }">
		<view class="empty-icon" v-if="showIcon">
			<image v-if="iconType === 'image'" :src="iconSrc" mode="aspectFit" class="icon-image"></image>
			<text v-else class="icon-emoji" :style="{ fontSize: iconSize + 'rpx' }">{{ getEmojiIcon() }}</text>
		</view>
		
		<view class="empty-content">
			<text class="empty-title" :style="{ fontSize: titleSize + 'rpx', color: titleColor }">
				{{ title }}
			</text>
			<text v-if="description" class="empty-description" :style="{ fontSize: descSize + 'rpx', color: descColor }">
				{{ description }}
			</text>
		</view>
		
		<view class="empty-actions" v-if="showAction">
			<button 
				class="action-btn" 
				:class="actionType"
				:style="{ 
					backgroundColor: actionBgColor,
					color: actionTextColor,
					fontSize: actionSize + 'rpx'
				}"
				@click="handleAction"
			>
				{{ actionText }}
			</button>
		</view>
	</view>
</template>

<script>
export default {
	name: 'EmptyState',
	props: {
		// 类型：data(无数据), network(网络错误), search(搜索无结果), error(错误)
		type: {
			type: String,
			default: 'data',
			validator: value => ['data', 'network', 'search', 'error', 'custom'].includes(value)
		},
		// 标题
		title: {
			type: String,
			default: ''
		},
		// 描述
		description: {
			type: String,
			default: ''
		},
		// 是否显示图标
		showIcon: {
			type: Boolean,
			default: true
		},
		// 图标类型：emoji 或 image
		iconType: {
			type: String,
			default: 'emoji',
			validator: value => ['emoji', 'image'].includes(value)
		},
		// 自定义图标（emoji 或图片路径）
		icon: {
			type: String,
			default: ''
		},
		// 图标大小
		iconSize: {
			type: Number,
			default: 120
		},
		// 标题大小
		titleSize: {
			type: Number,
			default: 32
		},
		// 标题颜色
		titleColor: {
			type: String,
			default: '#303133'
		},
		// 描述大小
		descSize: {
			type: Number,
			default: 26
		},
		// 描述颜色
		descColor: {
			type: String,
			default: '#909399'
		},
		// 是否显示操作按钮
		showAction: {
			type: Boolean,
			default: false
		},
		// 操作按钮文本
		actionText: {
			type: String,
			default: '重试'
		},
		// 操作按钮类型
		actionType: {
			type: String,
			default: 'primary',
			validator: value => ['primary', 'default', 'success', 'warning', 'danger'].includes(value)
		},
		// 操作按钮背景色
		actionBgColor: {
			type: String,
			default: ''
		},
		// 操作按钮文字颜色
		actionTextColor: {
			type: String,
			default: ''
		},
		// 操作按钮大小
		actionSize: {
			type: Number,
			default: 28
		},
		// 内边距
		padding: {
			type: Number,
			default: 100
		}
	},
	computed: {
		iconSrc() {
			if (this.iconType === 'image') {
				return this.icon || this.getDefaultImage()
			}
			return ''
		}
	},
	methods: {
		// 获取默认图片
		getDefaultImage() {
			const imageMap = {
				data: '/static/empty/no-data.png',
				network: '/static/empty/network-error.png',
				search: '/static/empty/no-search.png',
				error: '/static/empty/error.png'
			}
			return imageMap[this.type] || imageMap.data
		},
		
		// 获取emoji图标
		getEmojiIcon() {
			if (this.icon) return this.icon
			
			const emojiMap = {
				data: '📭',
				network: '🌐',
				search: '🔍',
				error: '❌',
				custom: '📄'
			}
			return emojiMap[this.type] || emojiMap.data
		},
		
		// 获取默认标题
		getDefaultTitle() {
			if (this.title) return this.title
			
			const titleMap = {
				data: '暂无数据',
				network: '网络连接失败',
				search: '搜索无结果',
				error: '出错了',
				custom: '空状态'
			}
			return titleMap[this.type] || titleMap.data
		},
		
		// 获取默认描述
		getDefaultDescription() {
			if (this.description) return this.description
			
			const descMap = {
				data: '当前没有可显示的内容',
				network: '请检查网络连接后重试',
				search: '试试其他关键词吧',
				error: '页面出现了一些问题',
				custom: ''
			}
			return descMap[this.type] || ''
		},
		
		// 处理操作按钮点击
		handleAction() {
			this.$emit('action', this.type)
		}
	},
	created() {
		// 设置默认值
		if (!this.title) {
			this.title = this.getDefaultTitle()
		}
		if (!this.description && this.type !== 'custom') {
			this.description = this.getDefaultDescription()
		}
	}
}
</script>

<style scoped>
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	text-align: center;
	min-height: 400rpx;
}

.empty-icon {
	margin-bottom: 30rpx;
}

.icon-image {
	width: 200rpx;
	height: 200rpx;
	opacity: 0.6;
}

.icon-emoji {
	display: block;
	opacity: 0.6;
}

.empty-content {
	margin-bottom: 40rpx;
}

.empty-title {
	display: block;
	font-weight: 500;
	margin-bottom: 15rpx;
	line-height: 1.4;
}

.empty-description {
	display: block;
	line-height: 1.5;
	max-width: 500rpx;
}

.empty-actions {
	display: flex;
	justify-content: center;
}

.action-btn {
	padding: 20rpx 40rpx;
	border-radius: 8rpx;
	border: none;
	font-weight: 500;
	transition: all 0.3s;
	min-width: 160rpx;
}

.action-btn.primary {
	background-color: #409EFF;
	color: white;
}

.action-btn.default {
	background-color: #f0f0f0;
	color: #606266;
	border: 1rpx solid #dcdfe6;
}

.action-btn.success {
	background-color: #67C23A;
	color: white;
}

.action-btn.warning {
	background-color: #E6A23C;
	color: white;
}

.action-btn.danger {
	background-color: #F56C6C;
	color: white;
}

.action-btn:active {
	opacity: 0.8;
	transform: scale(0.98);
}
</style>
