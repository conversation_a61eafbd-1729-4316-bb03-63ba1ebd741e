<template>
	<view class="loading-container" :class="{ 'overlay': overlay }" v-if="visible">
		<view class="loading-content">
			<view class="spinner" :class="spinnerType" :style="{ width: size + 'rpx', height: size + 'rpx' }">
				<view v-if="spinnerType === 'dots'" class="dots">
					<view class="dot" v-for="i in 3" :key="i"></view>
				</view>
				<view v-else-if="spinnerType === 'pulse'" class="pulse"></view>
				<view v-else-if="spinnerType === 'bars'" class="bars">
					<view class="bar" v-for="i in 5" :key="i"></view>
				</view>
			</view>
			<text v-if="text" class="loading-text" :style="{ color: textColor }">{{ text }}</text>
		</view>
	</view>
</template>

<script>
export default {
	name: 'LoadingSpinner',
	props: {
		// 是否显示
		visible: {
			type: Boolean,
			default: true
		},
		// 加载文本
		text: {
			type: String,
			default: '加载中...'
		},
		// 是否显示遮罩层
		overlay: {
			type: Boolean,
			default: true
		},
		// 加载器类型
		spinnerType: {
			type: String,
			default: 'spin', // spin, dots, pulse, bars
			validator: value => ['spin', 'dots', 'pulse', 'bars'].includes(value)
		},
		// 大小
		size: {
			type: Number,
			default: 60
		},
		// 颜色
		color: {
			type: String,
			default: '#409EFF'
		},
		// 文本颜色
		textColor: {
			type: String,
			default: '#666666'
		}
	}
}
</script>

<style scoped>
.loading-container {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
}

.loading-container.overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.8);
	z-index: 9999;
}

.loading-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.spinner {
	position: relative;
	display: inline-block;
}

/* 旋转加载器 */
.spinner.spin {
	border: 4rpx solid #f3f3f3;
	border-top: 4rpx solid var(--color, #409EFF);
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

/* 点状加载器 */
.spinner.dots .dots {
	display: flex;
	gap: 8rpx;
}

.spinner.dots .dot {
	width: 12rpx;
	height: 12rpx;
	background: var(--color, #409EFF);
	border-radius: 50%;
	animation: dotPulse 1.4s ease-in-out infinite both;
}

.spinner.dots .dot:nth-child(1) { animation-delay: -0.32s; }
.spinner.dots .dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes dotPulse {
	0%, 80%, 100% {
		transform: scale(0);
		opacity: 0.5;
	}
	40% {
		transform: scale(1);
		opacity: 1;
	}
}

/* 脉冲加载器 */
.spinner.pulse .pulse {
	width: 100%;
	height: 100%;
	background: var(--color, #409EFF);
	border-radius: 50%;
	animation: pulseScale 1s ease-in-out infinite;
}

@keyframes pulseScale {
	0% {
		transform: scale(0);
		opacity: 1;
	}
	100% {
		transform: scale(1);
		opacity: 0;
	}
}

/* 条状加载器 */
.spinner.bars .bars {
	display: flex;
	gap: 4rpx;
	align-items: end;
	height: 100%;
}

.spinner.bars .bar {
	width: 6rpx;
	background: var(--color, #409EFF);
	animation: barStretch 1.2s ease-in-out infinite;
}

.spinner.bars .bar:nth-child(1) { animation-delay: -1.2s; }
.spinner.bars .bar:nth-child(2) { animation-delay: -1.1s; }
.spinner.bars .bar:nth-child(3) { animation-delay: -1.0s; }
.spinner.bars .bar:nth-child(4) { animation-delay: -0.9s; }
.spinner.bars .bar:nth-child(5) { animation-delay: -0.8s; }

@keyframes barStretch {
	0%, 40%, 100% {
		height: 20%;
	}
	20% {
		height: 100%;
	}
}

.loading-text {
	margin-top: 20rpx;
	font-size: 28rpx;
	text-align: center;
}
</style>
