<template>
	<view class="dashboard-container">
		<!-- 顶部欢迎区域 -->
		<view class="welcome-section">
			<view class="welcome-content">
				<text class="welcome-title">欢迎回来，{{ adminUser.nickname || adminUser.username }}</text>
				<text class="welcome-subtitle">{{ currentTime }}</text>
			</view>
			<view class="logout-btn" @click="handleLogout">
				<text class="iconfont icon-logout"></text>
			</view>
		</view>
		
		<!-- 统计卡片区域 -->
		<view class="stats-section">
			<view class="stats-grid">
				<view class="stat-card" v-for="(item, index) in statsCards" :key="index">
					<view class="stat-icon" :style="{ backgroundColor: item.color }">
						<text class="iconfont" :class="item.icon"></text>
					</view>
					<view class="stat-content">
						<text class="stat-number">{{ item.value }}</text>
						<text class="stat-label">{{ item.label }}</text>
						<text class="stat-change" :class="item.trend">
							{{ item.change }}
						</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 图表区域 -->
		<view class="charts-section">
			<view class="chart-card">
				<view class="card-header">
					<text class="card-title">房源状态分布</text>
				</view>
				<view class="chart-content">
					<view class="pie-chart">
						<view class="chart-item" v-for="(item, index) in houseStatusData" :key="index">
							<view class="chart-color" :style="{ backgroundColor: item.color }"></view>
							<text class="chart-label">{{ item.label }}</text>
							<text class="chart-value">{{ item.value }}</text>
						</view>
					</view>
				</view>
			</view>
			
			<view class="chart-card">
				<view class="card-header">
					<text class="card-title">最近7天数据趋势</text>
				</view>
				<view class="chart-content">
					<view class="trend-chart">
						<view class="trend-item" v-for="(item, index) in trendData" :key="index">
							<text class="trend-date">{{ item.date }}</text>
							<view class="trend-bars">
								<view class="trend-bar user-bar" :style="{ height: item.users + '%' }"></view>
								<view class="trend-bar house-bar" :style="{ height: item.houses + '%' }"></view>
							</view>
						</view>
					</view>
					<view class="trend-legend">
						<view class="legend-item">
							<view class="legend-color user-color"></view>
							<text>新增用户</text>
						</view>
						<view class="legend-item">
							<view class="legend-color house-color"></view>
							<text>新增房源</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 快速操作区域 -->
		<view class="quick-actions">
			<text class="section-title">快速操作</text>
			<view class="actions-grid">
				<view class="action-item" v-for="(action, index) in quickActions" :key="index" @click="handleQuickAction(action)">
					<view class="action-icon" :style="{ backgroundColor: action.color }">
						<text class="iconfont" :class="action.icon"></text>
					</view>
					<text class="action-label">{{ action.label }}</text>
				</view>
			</view>
		</view>
		
		<!-- 加载状态 -->
		<view v-if="loading" class="loading-overlay">
			<view class="loading-spinner"></view>
			<text class="loading-text">加载中...</text>
		</view>
	</view>
</template>

<script>
import { apiService } from '@/utils/api.js'

export default {
	data() {
		return {
			loading: true,
			adminUser: {},
			currentTime: '',
			statsData: {},
			statsCards: [
				{
					label: '总用户数',
					value: 0,
					change: '+0',
					trend: 'up',
					icon: 'icon-users',
					color: '#409EFF'
				},
				{
					label: '总房源数',
					value: 0,
					change: '+0',
					trend: 'up',
					icon: 'icon-house',
					color: '#67C23A'
				},
				{
					label: '待审核',
					value: 0,
					change: '+0',
					trend: 'up',
					icon: 'icon-pending',
					color: '#E6A23C'
				},
				{
					label: '今日收藏',
					value: 0,
					change: '+0',
					trend: 'up',
					icon: 'icon-heart',
					color: '#F56C6C'
				}
			],
			houseStatusData: [
				{ label: '已审核', value: 0, color: '#67C23A' },
				{ label: '待审核', value: 0, color: '#E6A23C' },
				{ label: '已驳回', value: 0, color: '#F56C6C' }
			],
			trendData: [],
			quickActions: [
				{
					label: '房源管理',
					icon: 'icon-house',
					color: '#409EFF',
					action: 'house-manage'
				},
				{
					label: '用户管理',
					icon: 'icon-users',
					color: '#67C23A',
					action: 'user-manage'
				},
				{
					label: '系统设置',
					icon: 'icon-settings',
					color: '#E6A23C',
					action: 'settings'
				},
				{
					label: '审核房源',
					icon: 'icon-check',
					color: '#F56C6C',
					action: 'house-audit'
				}
			]
		}
	},
	onLoad() {
		this.checkAuth()
		this.updateCurrentTime()
		this.loadDashboardData()
		
		// 定时更新时间
		setInterval(() => {
			this.updateCurrentTime()
		}, 60000)
	},
	onShow() {
		// 页面显示时刷新数据
		this.loadDashboardData()
	},
	methods: {
		// 检查认证状态
		checkAuth() {
			const token = uni.getStorageSync('admin_token')
			const user = uni.getStorageSync('admin_user')
			
			if (!token || !user) {
				uni.reLaunch({
					url: '/pages/login/login'
				})
				return
			}
			
			this.adminUser = user
		},
		
		// 更新当前时间
		updateCurrentTime() {
			const now = new Date()
			const options = {
				year: 'numeric',
				month: 'long',
				day: 'numeric',
				hour: '2-digit',
				minute: '2-digit',
				weekday: 'long'
			}
			this.currentTime = now.toLocaleDateString('zh-CN', options)
		},
		
		// 加载仪表板数据
		async loadDashboardData() {
			this.loading = true
			
			try {
				const token = uni.getStorageSync('admin_token')
				const result = await apiService.get('/admin/statistics', { token })
				
				if (result.code === 0) {
					this.statsData = result.data
					this.updateStatsCards()
					this.updateHouseStatusData()
					this.generateTrendData()
				} else {
					uni.showToast({
						title: result.message || '获取数据失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('加载数据错误:', error)
				uni.showToast({
					title: '网络错误，请重试',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},
		
		// 更新统计卡片数据
		updateStatsCards() {
			const { users, houses, favorites } = this.statsData
			
			this.statsCards[0].value = users.total || 0
			this.statsCards[0].change = `+${users.today || 0}`
			
			this.statsCards[1].value = houses.total || 0
			this.statsCards[1].change = `+${houses.today || 0}`
			
			this.statsCards[2].value = houses.pending || 0
			this.statsCards[2].change = `待处理`
			
			this.statsCards[3].value = favorites.total || 0
			this.statsCards[3].change = `总收藏`
		},
		
		// 更新房源状态数据
		updateHouseStatusData() {
			const { houses } = this.statsData
			
			this.houseStatusData[0].value = houses.approved || 0
			this.houseStatusData[1].value = houses.pending || 0
			this.houseStatusData[2].value = houses.rejected || 0
		},
		
		// 生成趋势数据（模拟）
		generateTrendData() {
			const data = []
			for (let i = 6; i >= 0; i--) {
				const date = new Date()
				date.setDate(date.getDate() - i)
				
				data.push({
					date: `${date.getMonth() + 1}/${date.getDate()}`,
					users: Math.floor(Math.random() * 100),
					houses: Math.floor(Math.random() * 80)
				})
			}
			this.trendData = data
		},
		
		// 处理快速操作
		handleQuickAction(action) {
			switch (action.action) {
				case 'house-manage':
					uni.navigateTo({
						url: '/pages/house-manage/house-manage'
					})
					break
				case 'user-manage':
					uni.navigateTo({
						url: '/pages/user-manage/user-manage'
					})
					break
				case 'settings':
					uni.navigateTo({
						url: '/pages/settings/settings'
					})
					break
				case 'house-audit':
					uni.navigateTo({
						url: '/pages/house-manage/house-manage?tab=pending'
					})
					break
			}
		},
		
		// 处理登出
		handleLogout() {
			uni.showModal({
				title: '确认登出',
				content: '确定要退出管理后台吗？',
				success: (res) => {
					if (res.confirm) {
						uni.removeStorageSync('admin_token')
						uni.removeStorageSync('admin_user')
						uni.reLaunch({
							url: '/pages/login/login'
						})
					}
				}
			})
		}
	}
}
</script>

<style scoped>
.dashboard-container {
	min-height: 100vh;
	background: #f5f7fa;
	padding: 20rpx;
}

.welcome-section {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background: linear-gradient(135deg, #409EFF, #5dade2);
	border-radius: 20rpx;
	padding: 40rpx;
	margin-bottom: 30rpx;
	color: white;
}

.welcome-title {
	font-size: 36rpx;
	font-weight: bold;
	display: block;
	margin-bottom: 10rpx;
}

.welcome-subtitle {
	font-size: 26rpx;
	opacity: 0.8;
}

.logout-btn {
	padding: 20rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
}

.stats-section {
	margin-bottom: 30rpx;
}

.stats-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
}

.stat-card {
	background: white;
	border-radius: 16rpx;
	padding: 30rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.stat-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 20rpx;
}

.stat-icon .iconfont {
	font-size: 36rpx;
	color: white;
}

.stat-content {
	flex: 1;
}

.stat-number {
	font-size: 32rpx;
	font-weight: bold;
	color: #303133;
	display: block;
}

.stat-label {
	font-size: 24rpx;
	color: #909399;
	display: block;
	margin: 5rpx 0;
}

.stat-change {
	font-size: 22rpx;
}

.stat-change.up {
	color: #67C23A;
}

.charts-section {
	margin-bottom: 30rpx;
}

.chart-card {
	background: white;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.card-header {
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.card-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #303133;
}

.chart-content {
	padding: 30rpx;
}

.pie-chart {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.chart-item {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.chart-color {
	width: 20rpx;
	height: 20rpx;
	border-radius: 50%;
}

.chart-label {
	flex: 1;
	font-size: 28rpx;
	color: #606266;
}

.chart-value {
	font-size: 28rpx;
	font-weight: bold;
	color: #303133;
}

.trend-chart {
	display: flex;
	justify-content: space-between;
	align-items: end;
	height: 200rpx;
	margin-bottom: 20rpx;
}

.trend-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	flex: 1;
}

.trend-date {
	font-size: 22rpx;
	color: #909399;
	margin-bottom: 10rpx;
}

.trend-bars {
	display: flex;
	gap: 5rpx;
	height: 150rpx;
	align-items: end;
}

.trend-bar {
	width: 15rpx;
	border-radius: 2rpx;
	min-height: 10rpx;
}

.user-bar {
	background: #409EFF;
}

.house-bar {
	background: #67C23A;
}

.trend-legend {
	display: flex;
	justify-content: center;
	gap: 40rpx;
}

.legend-item {
	display: flex;
	align-items: center;
	gap: 10rpx;
	font-size: 24rpx;
	color: #606266;
}

.legend-color {
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
}

.user-color {
	background: #409EFF;
}

.house-color {
	background: #67C23A;
}

.quick-actions {
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #303133;
	display: block;
	margin-bottom: 20rpx;
}

.actions-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
}

.action-item {
	background: white;
	border-radius: 16rpx;
	padding: 40rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.action-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 20rpx;
}

.action-icon .iconfont {
	font-size: 36rpx;
	color: white;
}

.action-label {
	font-size: 28rpx;
	color: #303133;
}

.loading-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.8);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid #f3f3f3;
	border-top: 4rpx solid #409EFF;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	margin-top: 20rpx;
	font-size: 28rpx;
	color: #666;
}

/* 图标字体样式 */
.icon-logout::before { content: '🚪'; }
.icon-users::before { content: '👥'; }
.icon-house::before { content: '🏠'; }
.icon-pending::before { content: '⏳'; }
.icon-heart::before { content: '❤️'; }
.icon-settings::before { content: '⚙️'; }
.icon-check::before { content: '✅'; }
</style>
