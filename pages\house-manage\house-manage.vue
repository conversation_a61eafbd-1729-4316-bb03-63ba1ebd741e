<template>
	<view class="house-manage-container">
		<view class="search-section">
			<input
				class="search-input"
				type="text"
				v-model="searchKeyword"
				placeholder="Search houses"
			/>
		</view>
		
		<view class="house-list">
			<view class="house-item" v-for="house in houseList" :key="house._id">
				<text class="house-title">{{ house.title }}</text>
				<text class="house-price">{{ house.price }}</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			searchKeyword: '',
			houseList: []
		}
	},
	methods: {
		handleSearch() {
			console.log('Search:', this.searchKeyword)
		}
	}
}
</script>

<style scoped>
.house-manage-container {
	padding: 20rpx;
}

.search-input {
	width: 100%;
	height: 80rpx;
	border: 1rpx solid #ccc;
	padding: 0 20rpx;
}

.house-item {
	padding: 20rpx;
	border-bottom: 1rpx solid #eee;
}
</style>
