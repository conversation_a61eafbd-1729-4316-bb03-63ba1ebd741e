<template>
	<view class="login-container">
		<view class="login-header">
			<image class="logo" src="/static/logo.png" mode="aspectFit"></image>
			<text class="title">房屋租赁平台</text>
			<text class="subtitle">管理后台</text>
		</view>
		
		<view class="login-form">
			<view class="form-item">
				<view class="form-label">
					<text class="iconfont icon-user"></text>
					<text>用户名</text>
				</view>
				<input
					class="form-input"
					type="text"
					v-model="loginForm.username"
					placeholder="Enter username"
					:disabled="loading"
				/>
			</view>
			
			<view class="form-item">
				<view class="form-label">
					<text class="iconfont icon-lock"></text>
					<text>密码</text>
				</view>
				<input
					class="form-input"
					:type="showPassword ? 'text' : 'password'"
					v-model="loginForm.password"
					placeholder="Enter password"
					:disabled="loading"
				/>
				<text 
					class="password-toggle iconfont" 
					:class="showPassword ? 'icon-eye' : 'icon-eye-close'"
					@click="togglePassword"
				></text>
			</view>
			
			<view class="form-item">
				<checkbox-group @change="rememberChange">
					<label class="remember-label">
						<checkbox :checked="rememberMe" />
						<text>记住密码</text>
					</label>
				</checkbox-group>
			</view>
			
			<button 
				class="login-btn" 
				:class="{ 'loading': loading }"
				:disabled="loading || !canSubmit"
				@click="handleLogin"
			>
				<text v-if="loading">登录中...</text>
				<text v-else>登录</text>
			</button>
		</view>
		
		<view class="login-footer">
			<text class="footer-text">© 2024 房屋租赁平台 管理系统</text>
		</view>
	</view>
</template>

<script>
import { apiService } from '@/utils/api.js'

export default {
	data() {
		return {
			loginForm: {
				username: '',
				password: ''
			},
			showPassword: false,
			rememberMe: false,
			loading: false
		}
	},
	computed: {
		canSubmit() {
			return this.loginForm.username.trim() && this.loginForm.password.trim()
		}
	},
	onLoad() {
		// 检查是否已登录
		this.checkLoginStatus()
		// 加载记住的密码
		this.loadRememberedCredentials()
	},
	methods: {
		// 检查登录状态
		checkLoginStatus() {
			const token = uni.getStorageSync('admin_token')
			if (token) {
				// 验证token有效性
				this.verifyToken(token)
			}
		},
		
		// 验证token
		async verifyToken(token) {
			try {
				const result = await apiService.get('/admin/statistics', { token })
				if (result.code === 0) {
					// token有效，跳转到仪表板
					uni.reLaunch({
						url: '/pages/dashboard/dashboard'
					})
				}
			} catch (error) {
				// token无效，清除存储
				uni.removeStorageSync('admin_token')
				uni.removeStorageSync('admin_user')
			}
		},
		
		// 加载记住的凭据
		loadRememberedCredentials() {
			const remembered = uni.getStorageSync('remembered_admin')
			if (remembered) {
				this.loginForm.username = remembered.username || ''
				this.loginForm.password = remembered.password || ''
				this.rememberMe = true
			}
		},
		
		// 切换密码显示
		togglePassword() {
			this.showPassword = !this.showPassword
		},
		
		// 记住密码选择
		rememberChange(e) {
			this.rememberMe = e.detail.value.length > 0
		},
		
		// 处理登录
		async handleLogin() {
			if (!this.canSubmit) {
				uni.showToast({
					title: '请填写完整信息',
					icon: 'none'
				})
				return
			}
			
			this.loading = true
			
			try {
				// 调用登录API
				const result = await apiService.post('/user/login', {
					username: this.loginForm.username,
					password: this.loginForm.password
				})
				
				if (result.code === 0) {
					// 检查是否为管理员
					if (!result.data.role || !result.data.role.includes('admin')) {
						uni.showToast({
							title: '权限不足，仅限管理员登录',
							icon: 'none',
							duration: 3000
						})
						return
					}
					
					// 保存登录信息
					uni.setStorageSync('admin_token', result.data.token)
					uni.setStorageSync('admin_user', {
						uid: result.data.uid,
						username: result.data.username,
						nickname: result.data.nickname,
						role: result.data.role
					})
					
					// 保存记住的密码
					if (this.rememberMe) {
						uni.setStorageSync('remembered_admin', {
							username: this.loginForm.username,
							password: this.loginForm.password
						})
					} else {
						uni.removeStorageSync('remembered_admin')
					}
					
					uni.showToast({
						title: '登录成功',
						icon: 'success'
					})
					
					// 跳转到仪表板
					setTimeout(() => {
						uni.reLaunch({
							url: '/pages/dashboard/dashboard'
						})
					}, 1500)
				} else {
					uni.showToast({
						title: result.message || '登录失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('登录错误:', error)
				uni.showToast({
					title: '网络错误，请重试',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		}
	}
}
</script>

<style scoped>
.login-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
}

.login-header {
	text-align: center;
	margin-bottom: 80rpx;
}

.logo {
	width: 120rpx;
	height: 120rpx;
	margin-bottom: 20rpx;
}

.title {
	display: block;
	font-size: 48rpx;
	font-weight: bold;
	color: #ffffff;
	margin-bottom: 10rpx;
}

.subtitle {
	display: block;
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.8);
}

.login-form {
	width: 100%;
	max-width: 600rpx;
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 60rpx 40rpx;
	box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.1);
}

.form-item {
	margin-bottom: 40rpx;
	position: relative;
}

.form-label {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
	color: #666;
	font-size: 28rpx;
}

.iconfont {
	margin-right: 10rpx;
	font-size: 32rpx;
}

.form-input {
	width: 100%;
	height: 88rpx;
	border: 2rpx solid #e4e7ed;
	border-radius: 8rpx;
	padding: 0 20rpx;
	font-size: 28rpx;
	background: #fff;
	transition: border-color 0.3s;
}

.form-input:focus {
	border-color: #409EFF;
}

.password-toggle {
	position: absolute;
	right: 20rpx;
	top: 108rpx;
	color: #999;
	font-size: 32rpx;
}

.remember-label {
	display: flex;
	align-items: center;
	font-size: 26rpx;
	color: #666;
}

.remember-label text {
	margin-left: 10rpx;
}

.login-btn {
	width: 100%;
	height: 88rpx;
	background: linear-gradient(135deg, #409EFF, #5dade2);
	color: #ffffff;
	border: none;
	border-radius: 8rpx;
	font-size: 32rpx;
	font-weight: bold;
	margin-top: 40rpx;
	transition: all 0.3s;
}

.login-btn:disabled {
	background: #c0c4cc;
	color: #ffffff;
}

.login-btn.loading {
	background: #c0c4cc;
}

.login-footer {
	margin-top: 60rpx;
	text-align: center;
}

.footer-text {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.6);
}

/* 图标字体样式 */
.icon-user::before { content: '👤'; }
.icon-lock::before { content: '🔒'; }
.icon-eye::before { content: '👁'; }
.icon-eye-close::before { content: '🙈'; }
</style>
