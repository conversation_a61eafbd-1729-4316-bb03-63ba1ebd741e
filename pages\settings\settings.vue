<template>
	<view class="settings-container">
		<!-- 设置分类标签 -->
		<view class="settings-tabs">
			<view 
				class="settings-tab" 
				:class="{ active: currentTab === tab.value }"
				v-for="tab in settingsTabs" 
				:key="tab.value"
				@click="switchTab(tab.value)"
			>
				<text class="tab-icon iconfont" :class="tab.icon"></text>
				<text class="tab-label">{{ tab.label }}</text>
			</view>
		</view>
		
		<!-- 设置内容区域 -->
		<view class="settings-content">
			<!-- 基础设置 -->
			<view v-if="currentTab === 'basic'" class="setting-section">
				<view class="section-title">
					<text class="title-text">基础设置</text>
					<text class="title-desc">配置系统基本信息</text>
				</view>
				
				<view class="setting-item">
					<view class="item-header">
						<text class="item-title">系统公告</text>
						<text class="item-desc">显示在小程序首页的公告内容</text>
					</view>
					<textarea
						class="setting-textarea"
						v-model="settings.announcement"
						placeholder="Enter announcement content"
						maxlength="200"
					></textarea>
					<text class="char-count">{{ (settings.announcement || '').length }}/200</text>
				</view>
				
				<view class="setting-item">
					<view class="item-header">
						<text class="item-title">联系方式</text>
						<text class="item-desc">客服联系信息</text>
					</view>
					<view class="contact-form">
						<view class="form-row">
							<text class="form-label">客服电话：</text>
							<input
								class="form-input"
								v-model="settings.contact_info.phone"
								placeholder="Enter phone number"
							/>
						</view>
						<view class="form-row">
							<text class="form-label">客服邮箱：</text>
							<input
								class="form-input"
								v-model="settings.contact_info.email"
								placeholder="Enter email address"
							/>
						</view>
						<view class="form-row">
							<text class="form-label">客服微信：</text>
							<input
								class="form-input"
								v-model="settings.contact_info.wechat"
								placeholder="Enter WeChat ID"
							/>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 轮播图设置 -->
			<view v-if="currentTab === 'banner'" class="setting-section">
				<view class="section-title">
					<text class="title-text">轮播图管理</text>
					<text class="title-desc">配置首页轮播图内容</text>
				</view>
				
				<view class="banner-list">
					<view 
						class="banner-item" 
						v-for="(banner, index) in settings.banner_list" 
						:key="index"
					>
						<view class="banner-preview">
							<image 
								:src="banner.image || '/static/default-banner.png'" 
								mode="aspectFill"
							></image>
						</view>
						<view class="banner-form">
							<view class="form-row">
								<text class="form-label">标题：</text>
								<input
									class="form-input"
									v-model="banner.title"
									placeholder="Enter banner title"
								/>
							</view>
							<view class="form-row">
								<text class="form-label">图片URL：</text>
								<input
									class="form-input"
									v-model="banner.image"
									placeholder="Enter image URL"
								/>
							</view>
							<view class="form-row">
								<text class="form-label">跳转链接：</text>
								<input
									class="form-input"
									v-model="banner.link"
									placeholder="Enter link URL (optional)"
								/>
							</view>
						</view>
						<view class="banner-actions">
							<button class="action-btn delete-btn" @click="removeBanner(index)">删除</button>
						</view>
					</view>
				</view>
				
				<button class="add-banner-btn" @click="addBanner">
					<text class="iconfont icon-plus"></text>
					<text>添加轮播图</text>
				</button>
			</view>
			
			<!-- 搜索设置 -->
			<view v-if="currentTab === 'search'" class="setting-section">
				<view class="section-title">
					<text class="title-text">搜索设置</text>
					<text class="title-desc">配置搜索热词和推荐</text>
				</view>
				
				<view class="setting-item">
					<view class="item-header">
						<text class="item-title">搜索热词</text>
						<text class="item-desc">用户搜索时的推荐关键词</text>
					</view>
					<view class="keywords-container">
						<view class="keyword-tags">
							<view 
								class="keyword-tag" 
								v-for="(keyword, index) in settings.search_keywords" 
								:key="index"
							>
								<text>{{ keyword }}</text>
								<text class="tag-remove" @click="removeKeyword(index)">×</text>
							</view>
						</view>
						<view class="add-keyword">
							<input
								class="keyword-input"
								v-model="newKeyword"
								placeholder="Enter keyword and press enter"
								@confirm="addKeyword"
							/>
							<button class="add-keyword-btn" @click="addKeyword">添加</button>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 账号设置 -->
			<view v-if="currentTab === 'account'" class="setting-section">
				<view class="section-title">
					<text class="title-text">账号设置</text>
					<text class="title-desc">管理员账号相关设置</text>
				</view>
				
				<view class="setting-item">
					<view class="item-header">
						<text class="item-title">修改密码</text>
						<text class="item-desc">更改当前管理员账号密码</text>
					</view>
					<view class="password-form">
						<view class="form-row">
							<text class="form-label">当前密码：</text>
							<input
								class="form-input"
								type="password"
								v-model="passwordForm.oldPassword"
								placeholder="Enter current password"
							/>
						</view>
						<view class="form-row">
							<text class="form-label">新密码：</text>
							<input
								class="form-input"
								type="password"
								v-model="passwordForm.newPassword"
								placeholder="Enter new password"
							/>
						</view>
						<view class="form-row">
							<text class="form-label">确认密码：</text>
							<input
								class="form-input"
								type="password"
								v-model="passwordForm.confirmPassword"
								placeholder="Confirm new password"
							/>
						</view>
						<button class="change-password-btn" @click="changePassword">修改密码</button>
					</view>
				</view>
				
				<view class="setting-item">
					<view class="item-header">
						<text class="item-title">退出登录</text>
						<text class="item-desc">退出当前管理员账号</text>
					</view>
					<button class="logout-btn" @click="handleLogout">退出登录</button>
				</view>
			</view>
		</view>
		
		<!-- 保存按钮 -->
		<view class="save-section" v-if="currentTab !== 'account'">
			<button class="save-btn" :disabled="saving" @click="saveSettings">
				<text v-if="saving">保存中...</text>
				<text v-else>保存设置</text>
			</button>
		</view>
		
		<!-- 加载状态 -->
		<view v-if="loading" class="loading-overlay">
			<view class="loading-spinner"></view>
			<text class="loading-text">加载中...</text>
		</view>
	</view>
</template>

<script>
import { apiService } from '@/utils/api.js'

export default {
	data() {
		return {
			loading: false,
			saving: false,
			currentTab: 'basic',
			settingsTabs: [
				{
					label: '基础设置',
					value: 'basic',
					icon: 'icon-settings'
				},
				{
					label: '轮播图',
					value: 'banner',
					icon: 'icon-image'
				},
				{
					label: '搜索设置',
					value: 'search',
					icon: 'icon-search'
				},
				{
					label: '账号设置',
					value: 'account',
					icon: 'icon-user'
				}
			],
			settings: {
				announcement: '',
				contact_info: {
					phone: '',
					email: '',
					wechat: ''
				},
				banner_list: [],
				search_keywords: []
			},
			newKeyword: '',
			passwordForm: {
				oldPassword: '',
				newPassword: '',
				confirmPassword: ''
			}
		}
	},
	onLoad() {
		this.checkAuth()
		this.loadSettings()
	},
	methods: {
		// 检查认证状态
		checkAuth() {
			const token = uni.getStorageSync('admin_token')
			if (!token) {
				uni.reLaunch({
					url: '/pages/login/login'
				})
			}
		},
		
		// 切换标签
		switchTab(tab) {
			this.currentTab = tab
		},
		
		// 加载设置
		async loadSettings() {
			this.loading = true
			
			try {
				const token = uni.getStorageSync('admin_token')
				const result = await apiService.get('/admin/config', { token })
				
				if (result.code === 0) {
					// 合并默认设置和服务器设置
					this.settings = {
						announcement: result.data.announcement || '',
						contact_info: result.data.contact_info || {
							phone: '',
							email: '',
							wechat: ''
						},
						banner_list: result.data.banner_list || [],
						search_keywords: result.data.search_keywords || []
					}
				} else {
					uni.showToast({
						title: result.message || '获取设置失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('加载设置错误:', error)
				uni.showToast({
					title: '网络错误，请重试',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},
		
		// 保存设置
		async saveSettings() {
			this.saving = true
			
			try {
				const token = uni.getStorageSync('admin_token')
				
				// 根据当前标签保存对应的设置
				let configKey = ''
				let configValue = null
				let description = ''
				
				switch (this.currentTab) {
					case 'basic':
						// 保存公告
						await this.saveConfig('announcement', this.settings.announcement, '系统公告')
						// 保存联系方式
						await this.saveConfig('contact_info', this.settings.contact_info, '联系方式')
						break
					case 'banner':
						await this.saveConfig('banner_list', this.settings.banner_list, '轮播图配置')
						break
					case 'search':
						await this.saveConfig('search_keywords', this.settings.search_keywords, '搜索热词')
						break
				}
				
				uni.showToast({
					title: '保存成功',
					icon: 'success'
				})
			} catch (error) {
				console.error('保存设置错误:', error)
				uni.showToast({
					title: '保存失败，请重试',
					icon: 'none'
				})
			} finally {
				this.saving = false
			}
		},
		
		// 保存单个配置项
		async saveConfig(key, value, description) {
			const token = uni.getStorageSync('admin_token')
			const result = await apiService.put('/admin/config', {
				token,
				key,
				value,
				description
			})
			
			if (result.code !== 0) {
				throw new Error(result.message || '保存失败')
			}
		},
		
		// 添加轮播图
		addBanner() {
			this.settings.banner_list.push({
				title: '',
				image: '',
				link: ''
			})
		},
		
		// 删除轮播图
		removeBanner(index) {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这个轮播图吗？',
				success: (res) => {
					if (res.confirm) {
						this.settings.banner_list.splice(index, 1)
					}
				}
			})
		},
		
		// 添加关键词
		addKeyword() {
			const keyword = this.newKeyword.trim()
			if (!keyword) {
				uni.showToast({
					title: '请输入关键词',
					icon: 'none'
				})
				return
			}
			
			if (this.settings.search_keywords.includes(keyword)) {
				uni.showToast({
					title: '关键词已存在',
					icon: 'none'
				})
				return
			}
			
			this.settings.search_keywords.push(keyword)
			this.newKeyword = ''
		},
		
		// 删除关键词
		removeKeyword(index) {
			this.settings.search_keywords.splice(index, 1)
		},
		
		// 修改密码
		async changePassword() {
			const { oldPassword, newPassword, confirmPassword } = this.passwordForm
			
			if (!oldPassword || !newPassword || !confirmPassword) {
				uni.showToast({
					title: '请填写完整信息',
					icon: 'none'
				})
				return
			}
			
			if (newPassword !== confirmPassword) {
				uni.showToast({
					title: '两次输入的密码不一致',
					icon: 'none'
				})
				return
			}
			
			if (newPassword.length < 6) {
				uni.showToast({
					title: '新密码长度不能少于6位',
					icon: 'none'
				})
				return
			}
			
			try {
				const token = uni.getStorageSync('admin_token')
				const result = await apiService.put('/user/password', {
					token,
					oldPassword,
					newPassword
				})
				
				if (result.code === 0) {
					uni.showToast({
						title: '密码修改成功',
						icon: 'success'
					})
					
					// 清空表单
					this.passwordForm = {
						oldPassword: '',
						newPassword: '',
						confirmPassword: ''
					}
				} else {
					uni.showToast({
						title: result.message || '修改失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('修改密码错误:', error)
				uni.showToast({
					title: '网络错误，请重试',
					icon: 'none'
				})
			}
		},
		
		// 退出登录
		handleLogout() {
			uni.showModal({
				title: '确认退出',
				content: '确定要退出管理后台吗？',
				success: (res) => {
					if (res.confirm) {
						uni.removeStorageSync('admin_token')
						uni.removeStorageSync('admin_user')
						uni.reLaunch({
							url: '/pages/login/login'
						})
					}
				}
			})
		}
	}
}
</script>

<style scoped>
.settings-container {
	min-height: 100vh;
	background: #f5f7fa;
	padding: 20rpx;
}

.settings-tabs {
	display: flex;
	background: white;
	border-radius: 16rpx;
	padding: 10rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.settings-tab {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx;
	border-radius: 12rpx;
	transition: all 0.3s;
}

.settings-tab.active {
	background: #409EFF;
	color: white;
}

.tab-icon {
	font-size: 32rpx;
	margin-bottom: 10rpx;
}

.tab-label {
	font-size: 24rpx;
}

.settings-content {
	margin-bottom: 20rpx;
}

.setting-section {
	background: white;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
	margin-bottom: 40rpx;
	border-bottom: 1rpx solid #f0f0f0;
	padding-bottom: 20rpx;
}

.title-text {
	font-size: 36rpx;
	font-weight: bold;
	color: #303133;
	display: block;
	margin-bottom: 10rpx;
}

.title-desc {
	font-size: 26rpx;
	color: #909399;
}

.setting-item {
	margin-bottom: 40rpx;
}

.item-header {
	margin-bottom: 20rpx;
}

.item-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #303133;
	display: block;
	margin-bottom: 10rpx;
}

.item-desc {
	font-size: 26rpx;
	color: #606266;
}

.setting-textarea {
	width: 100%;
	min-height: 200rpx;
	border: 2rpx solid #e4e7ed;
	border-radius: 8rpx;
	padding: 20rpx;
	font-size: 28rpx;
	resize: none;
}

.char-count {
	display: block;
	text-align: right;
	font-size: 24rpx;
	color: #909399;
	margin-top: 10rpx;
}

.contact-form, .password-form {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.form-row {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.form-label {
	width: 150rpx;
	font-size: 28rpx;
	color: #606266;
	flex-shrink: 0;
}

.form-input {
	flex: 1;
	height: 80rpx;
	border: 2rpx solid #e4e7ed;
	border-radius: 8rpx;
	padding: 0 20rpx;
	font-size: 28rpx;
}

.banner-list {
	margin-bottom: 30rpx;
}

.banner-item {
	border: 2rpx solid #e4e7ed;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	gap: 30rpx;
}

.banner-preview {
	width: 200rpx;
	height: 120rpx;
	border-radius: 8rpx;
	overflow: hidden;
	flex-shrink: 0;
}

.banner-preview image {
	width: 100%;
	height: 100%;
}

.banner-form {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.banner-actions {
	display: flex;
	align-items: center;
}

.action-btn {
	padding: 15rpx 30rpx;
	border-radius: 8rpx;
	font-size: 24rpx;
	border: none;
	color: white;
}

.delete-btn {
	background: #F56C6C;
}

.add-banner-btn {
	width: 100%;
	height: 80rpx;
	border: 2rpx dashed #409EFF;
	border-radius: 8rpx;
	background: transparent;
	color: #409EFF;
	font-size: 28rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 10rpx;
}

.keywords-container {
	border: 2rpx solid #e4e7ed;
	border-radius: 8rpx;
	padding: 20rpx;
}

.keyword-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 15rpx;
	margin-bottom: 20rpx;
}

.keyword-tag {
	display: flex;
	align-items: center;
	gap: 10rpx;
	padding: 10rpx 20rpx;
	background: #409EFF;
	color: white;
	border-radius: 20rpx;
	font-size: 26rpx;
}

.tag-remove {
	font-size: 32rpx;
	cursor: pointer;
}

.add-keyword {
	display: flex;
	gap: 20rpx;
}

.keyword-input {
	flex: 1;
	height: 60rpx;
	border: 1rpx solid #e4e7ed;
	border-radius: 6rpx;
	padding: 0 15rpx;
	font-size: 26rpx;
}

.add-keyword-btn {
	padding: 15rpx 30rpx;
	background: #409EFF;
	color: white;
	border: none;
	border-radius: 6rpx;
	font-size: 26rpx;
}

.change-password-btn {
	width: 200rpx;
	height: 70rpx;
	background: #409EFF;
	color: white;
	border: none;
	border-radius: 8rpx;
	font-size: 28rpx;
	margin-top: 20rpx;
}

.logout-btn {
	width: 200rpx;
	height: 70rpx;
	background: #F56C6C;
	color: white;
	border: none;
	border-radius: 8rpx;
	font-size: 28rpx;
}

.save-section {
	background: white;
	border-radius: 16rpx;
	padding: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.save-btn {
	width: 100%;
	height: 88rpx;
	background: #67C23A;
	color: white;
	border: none;
	border-radius: 8rpx;
	font-size: 32rpx;
	font-weight: bold;
}

.save-btn:disabled {
	background: #c0c4cc;
}

.loading-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.8);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid #f3f3f3;
	border-top: 4rpx solid #409EFF;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	margin-top: 20rpx;
	font-size: 28rpx;
	color: #666;
}

/* 图标字体样式 */
.icon-settings::before { content: '⚙️'; }
.icon-image::before { content: '🖼️'; }
.icon-search::before { content: '🔍'; }
.icon-user::before { content: '👤'; }
.icon-plus::before { content: '➕'; }
</style>
