<template>
	<view class="test-container">
		<text>Test Page</text>
		<input 
			type="text" 
			placeholder="Test input"
			v-model="testValue"
		/>
		<button @click="handleClick">Test Button</button>
	</view>
</template>

<script>
export default {
	data() {
		return {
			testValue: ''
		}
	},
	methods: {
		handleClick() {
			console.log('Test clicked')
		}
	}
}
</script>

<style scoped>
.test-container {
	padding: 20px;
}
</style>
