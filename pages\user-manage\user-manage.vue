<template>
	<view class="user-manage-container">
		<!-- 搜索和筛选区域 -->
		<view class="search-section">
			<view class="search-bar">
				<input
					class="search-input"
					type="text"
					v-model="searchKeyword"
					placeholder="Search users"
					@input="handleSearch"
				/>
				<text class="search-icon">🔍</text>
			</view>
			
			<view class="filter-tabs">
				<view 
					class="filter-tab" 
					:class="{ active: currentTab === tab.value }"
					v-for="tab in statusTabs" 
					:key="tab.value"
					@click="switchTab(tab.value)"
				>
					<text>{{ tab.label }}</text>
					<text class="tab-count" v-if="tab.count > 0">({{ tab.count }})</text>
				</view>
			</view>
		</view>
		
		<!-- 用户列表 -->
		<view class="user-list">
			<view 
				class="user-item" 
				v-for="user in userList" 
				:key="user._id"
				@click="viewUserDetail(user)"
			>
				<view class="user-avatar">
					<image 
						:src="user.avatar || '/static/default-avatar.png'" 
						mode="aspectFill"
					></image>
					<view class="status-indicator" :class="{ banned: user.is_banned }"></view>
				</view>
				
				<view class="user-content">
					<view class="user-header">
						<text class="user-name">{{ user.nickname || user.username }}</text>
						<view class="user-badges">
							<text class="role-badge admin" v-if="user.role && user.role.includes('admin')">管理员</text>
							<text class="status-badge banned" v-if="user.is_banned">已封禁</text>
						</view>
					</view>
					
					<view class="user-info">
						<text class="user-username">@{{ user.username }}</text>
						<text class="user-phone" v-if="user.phone">{{ formatPhone(user.phone) }}</text>
						<text class="user-email" v-if="user.email">{{ formatEmail(user.email) }}</text>
					</view>
					
					<view class="user-meta">
						<text class="user-join-time">注册时间：{{ formatTime(user.created_at) }}</text>
						<text class="user-login-time" v-if="user.last_login_at">
							最后登录：{{ formatTime(user.last_login_at) }}
						</text>
					</view>
					
					<view class="user-stats">
						<view class="stat-item">
							<text class="stat-label">发布房源</text>
							<text class="stat-value">{{ user.house_count || 0 }}</text>
						</view>
						<view class="stat-item">
							<text class="stat-label">收藏数量</text>
							<text class="stat-value">{{ user.favorite_count || 0 }}</text>
						</view>
					</view>
					
					<view class="user-actions" v-if="!isCurrentAdmin(user)">
						<button 
							class="action-btn unban-btn" 
							v-if="user.is_banned"
							@click.stop="unbanUser(user._id, user.username)"
						>
							解封用户
						</button>
						<button 
							class="action-btn ban-btn" 
							v-else
							@click.stop="banUser(user._id, user.username)"
						>
							封禁用户
						</button>
						<button 
							class="action-btn detail-btn"
							@click.stop="viewUserDetail(user)"
						>
							查看详情
						</button>
					</view>
				</view>
			</view>
			
			<!-- 空状态 -->
			<view v-if="!loading && userList.length === 0" class="empty-state">
				<text class="empty-icon">👥</text>
				<text class="empty-text">暂无用户数据</text>
			</view>
		</view>
		
		<!-- 分页 -->
		<view class="pagination" v-if="totalPages > 1">
			<button 
				class="page-btn" 
				:disabled="currentPage <= 1"
				@click="changePage(currentPage - 1)"
			>
				上一页
			</button>
			<text class="page-info">{{ currentPage }} / {{ totalPages }}</text>
			<button 
				class="page-btn" 
				:disabled="currentPage >= totalPages"
				@click="changePage(currentPage + 1)"
			>
				下一页
			</button>
		</view>
		
		<!-- 用户详情弹窗 -->
		<view v-if="showDetailDialog" class="modal-overlay" @click="hideDetailModal">
			<view class="modal-content detail-modal" @click.stop>
				<view class="modal-header">
					<text class="modal-title">用户详情</text>
					<text class="modal-close" @click="hideDetailModal">×</text>
				</view>
				<view class="modal-body" v-if="currentDetailUser">
					<view class="detail-section">
						<view class="detail-avatar">
							<image 
								:src="currentDetailUser.avatar || '/static/default-avatar.png'" 
								mode="aspectFill"
							></image>
						</view>
						<view class="detail-info">
							<view class="detail-item">
								<text class="detail-label">用户名：</text>
								<text class="detail-value">{{ currentDetailUser.username }}</text>
							</view>
							<view class="detail-item">
								<text class="detail-label">昵称：</text>
								<text class="detail-value">{{ currentDetailUser.nickname || '未设置' }}</text>
							</view>
							<view class="detail-item">
								<text class="detail-label">手机号：</text>
								<text class="detail-value">{{ currentDetailUser.phone || '未绑定' }}</text>
							</view>
							<view class="detail-item">
								<text class="detail-label">邮箱：</text>
								<text class="detail-value">{{ currentDetailUser.email || '未绑定' }}</text>
							</view>
							<view class="detail-item">
								<text class="detail-label">角色：</text>
								<text class="detail-value">{{ getRoleText(currentDetailUser.role) }}</text>
							</view>
							<view class="detail-item">
								<text class="detail-label">状态：</text>
								<text class="detail-value" :class="{ banned: currentDetailUser.is_banned }">
									{{ currentDetailUser.is_banned ? '已封禁' : '正常' }}
								</text>
							</view>
							<view class="detail-item">
								<text class="detail-label">注册时间：</text>
								<text class="detail-value">{{ formatTime(currentDetailUser.created_at) }}</text>
							</view>
							<view class="detail-item" v-if="currentDetailUser.last_login_at">
								<text class="detail-label">最后登录：</text>
								<text class="detail-value">{{ formatTime(currentDetailUser.last_login_at) }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 加载状态 -->
		<view v-if="loading" class="loading-overlay">
			<view class="loading-spinner"></view>
			<text class="loading-text">加载中...</text>
		</view>
	</view>
</template>

<script>
import { apiService } from '@/utils/api.js'

export default {
	data() {
		return {
			loading: false,
			searchKeyword: '',
			currentTab: 'all',
			currentPage: 1,
			pageSize: 10,
			totalPages: 1,
			userList: [],
			statusTabs: [
				{ label: '全部', value: 'all', count: 0 },
				{ label: '正常用户', value: 'normal', count: 0 },
				{ label: '已封禁', value: 'banned', count: 0 },
				{ label: '管理员', value: 'admin', count: 0 }
			],
			showDetailDialog: false,
			currentDetailUser: null,
			searchTimer: null,
			currentAdminId: ''
		}
	},
	onLoad() {
		this.checkAuth()
		this.loadUserList()
	},
	onShow() {
		// 页面显示时刷新数据
		this.loadUserList()
	},
	methods: {
		// 检查认证状态
		checkAuth() {
			const token = uni.getStorageSync('admin_token')
			const user = uni.getStorageSync('admin_user')
			if (!token || !user) {
				uni.reLaunch({
					url: '/pages/login/login'
				})
				return
			}
			this.currentAdminId = user.uid
		},
		
		// 切换标签
		switchTab(tab) {
			this.currentTab = tab
			this.currentPage = 1
			this.loadUserList()
		},
		
		// 处理搜索
		handleSearch() {
			// 防抖处理
			clearTimeout(this.searchTimer)
			this.searchTimer = setTimeout(() => {
				this.currentPage = 1
				this.loadUserList()
			}, 500)
		},
		
		// 加载用户列表
		async loadUserList() {
			this.loading = true
			
			try {
				const token = uni.getStorageSync('admin_token')
				const params = {
					token,
					page: this.currentPage,
					limit: this.pageSize
				}
				
				// 添加状态筛选
				if (this.currentTab === 'banned') {
					params.is_banned = true
				} else if (this.currentTab === 'normal') {
					params.is_banned = false
				}
				
				// 添加搜索关键词
				if (this.searchKeyword.trim()) {
					params.keyword = this.searchKeyword.trim()
				}
				
				const result = await apiService.get('/admin/users', params)
				
				if (result.code === 0) {
					this.userList = result.data.list
					this.totalPages = result.data.totalPages
					this.updateTabCounts()
				} else {
					uni.showToast({
						title: result.message || '获取数据失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('加载用户列表错误:', error)
				uni.showToast({
					title: '网络错误，请重试',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},
		
		// 更新标签计数
		async updateTabCounts() {
			try {
				const token = uni.getStorageSync('admin_token')
				const result = await apiService.get('/admin/statistics', { token })
				
				if (result.code === 0) {
					const { users } = result.data
					this.statusTabs[0].count = users.total || 0
					this.statusTabs[1].count = users.active || 0
					this.statusTabs[2].count = users.banned || 0
					// 管理员数量需要单独计算
					this.statusTabs[3].count = this.userList.filter(user => 
						user.role && user.role.includes('admin')
					).length
				}
			} catch (error) {
				console.error('更新计数错误:', error)
			}
		},
		
		// 封禁用户
		async banUser(userId, username) {
			uni.showModal({
				title: '确认封禁',
				content: `确定要封禁用户"${username}"吗？封禁后该用户将无法登录和使用系统。`,
				success: async (res) => {
					if (res.confirm) {
						try {
							const token = uni.getStorageSync('admin_token')
							const result = await apiService.put(`/admin/user/ban/${userId}`, { token })
							
							if (result.code === 0) {
								uni.showToast({
									title: '封禁成功',
									icon: 'success'
								})
								this.loadUserList()
							} else {
								uni.showToast({
									title: result.message || '操作失败',
									icon: 'none'
								})
							}
						} catch (error) {
							console.error('封禁用户错误:', error)
							uni.showToast({
								title: '网络错误，请重试',
								icon: 'none'
							})
						}
					}
				}
			})
		},
		
		// 解封用户
		async unbanUser(userId, username) {
			uni.showModal({
				title: '确认解封',
				content: `确定要解封用户"${username}"吗？`,
				success: async (res) => {
					if (res.confirm) {
						try {
							const token = uni.getStorageSync('admin_token')
							const result = await apiService.put(`/admin/user/unban/${userId}`, { token })
							
							if (result.code === 0) {
								uni.showToast({
									title: '解封成功',
									icon: 'success'
								})
								this.loadUserList()
							} else {
								uni.showToast({
									title: result.message || '操作失败',
									icon: 'none'
								})
							}
						} catch (error) {
							console.error('解封用户错误:', error)
							uni.showToast({
								title: '网络错误，请重试',
								icon: 'none'
							})
						}
					}
				}
			})
		},
		
		// 查看用户详情
		viewUserDetail(user) {
			this.currentDetailUser = user
			this.showDetailDialog = true
		},
		
		// 隐藏详情弹窗
		hideDetailModal() {
			this.showDetailDialog = false
			this.currentDetailUser = null
		},
		
		// 切换页码
		changePage(page) {
			this.currentPage = page
			this.loadUserList()
		},
		
		// 判断是否为当前管理员
		isCurrentAdmin(user) {
			return user._id === this.currentAdminId
		},
		
		// 获取角色文本
		getRoleText(roles) {
			if (!roles || !Array.isArray(roles)) return '普通用户'
			if (roles.includes('admin')) return '管理员'
			return '普通用户'
		},
		
		// 格式化手机号
		formatPhone(phone) {
			if (!phone) return ''
			return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
		},
		
		// 格式化邮箱
		formatEmail(email) {
			if (!email) return ''
			const [username, domain] = email.split('@')
			if (username.length > 2) {
				const maskedUsername = username.substring(0, 2) + '*'.repeat(username.length - 2)
				return maskedUsername + '@' + domain
			}
			return email
		},
		
		// 格式化时间
		formatTime(time) {
			if (!time) return ''
			const date = new Date(time)
			return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
		}
	}
}
</script>

<style scoped>
.user-manage-container {
	min-height: 100vh;
	background: #f5f7fa;
	padding: 20rpx;
}

.search-section {
	background: white;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.search-bar {
	position: relative;
	margin-bottom: 30rpx;
}

.search-input {
	width: 100%;
	height: 80rpx;
	border: 2rpx solid #e4e7ed;
	border-radius: 40rpx;
	padding: 0 50rpx 0 30rpx;
	font-size: 28rpx;
	background: #f8f9fa;
}

.search-icon {
	position: absolute;
	right: 30rpx;
	top: 50%;
	transform: translateY(-50%);
	font-size: 32rpx;
	color: #909399;
}

.filter-tabs {
	display: flex;
	gap: 20rpx;
}

.filter-tab {
	flex: 1;
	text-align: center;
	padding: 20rpx;
	border-radius: 8rpx;
	background: #f8f9fa;
	color: #606266;
	font-size: 26rpx;
	transition: all 0.3s;
}

.filter-tab.active {
	background: #409EFF;
	color: white;
}

.tab-count {
	margin-left: 10rpx;
	font-size: 22rpx;
}

.user-list {
	margin-bottom: 20rpx;
}

.user-item {
	background: white;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	display: flex;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.user-avatar {
	position: relative;
	margin-right: 30rpx;
}

.user-avatar image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
}

.status-indicator {
	position: absolute;
	bottom: 5rpx;
	right: 5rpx;
	width: 24rpx;
	height: 24rpx;
	border-radius: 50%;
	background: #67C23A;
	border: 4rpx solid white;
}

.status-indicator.banned {
	background: #F56C6C;
}

.user-content {
	flex: 1;
}

.user-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.user-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #303133;
}

.user-badges {
	display: flex;
	gap: 10rpx;
}

.role-badge, .status-badge {
	padding: 5rpx 15rpx;
	border-radius: 12rpx;
	font-size: 22rpx;
	color: white;
}

.role-badge.admin {
	background: #409EFF;
}

.status-badge.banned {
	background: #F56C6C;
}

.user-info {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
	margin-bottom: 15rpx;
	font-size: 26rpx;
	color: #606266;
}

.user-meta {
	margin-bottom: 20rpx;
	font-size: 24rpx;
	color: #909399;
}

.user-stats {
	display: flex;
	gap: 40rpx;
	margin-bottom: 20rpx;
}

.stat-item {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.stat-label {
	font-size: 24rpx;
	color: #909399;
	margin-bottom: 5rpx;
}

.stat-value {
	font-size: 28rpx;
	font-weight: bold;
	color: #409EFF;
}

.user-actions {
	display: flex;
	gap: 20rpx;
	flex-wrap: wrap;
}

.action-btn {
	padding: 15rpx 30rpx;
	border-radius: 8rpx;
	font-size: 24rpx;
	border: none;
	color: white;
}

.ban-btn {
	background: #F56C6C;
}

.unban-btn {
	background: #67C23A;
}

.detail-btn {
	background: #409EFF;
}

.empty-state {
	text-align: center;
	padding: 100rpx 0;
	color: #909399;
}

.empty-icon {
	font-size: 120rpx;
	display: block;
	margin-bottom: 20rpx;
}

.empty-text {
	font-size: 28rpx;
}

.pagination {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 30rpx;
	padding: 30rpx;
	background: white;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.page-btn {
	padding: 20rpx 40rpx;
	border-radius: 8rpx;
	background: #409EFF;
	color: white;
	border: none;
	font-size: 26rpx;
}

.page-btn:disabled {
	background: #c0c4cc;
}

.page-info {
	font-size: 26rpx;
	color: #606266;
}

.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.modal-content {
	background: white;
	border-radius: 16rpx;
	width: 90%;
	max-width: 600rpx;
	max-height: 80vh;
	overflow: hidden;
}

.detail-modal {
	max-width: 700rpx;
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #303133;
}

.modal-close {
	font-size: 40rpx;
	color: #909399;
	cursor: pointer;
}

.modal-body {
	padding: 30rpx;
	max-height: 60vh;
	overflow-y: auto;
}

.detail-section {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.detail-avatar {
	margin-bottom: 30rpx;
}

.detail-avatar image {
	width: 150rpx;
	height: 150rpx;
	border-radius: 50%;
}

.detail-info {
	width: 100%;
}

.detail-item {
	display: flex;
	margin-bottom: 20rpx;
	font-size: 28rpx;
}

.detail-label {
	width: 150rpx;
	color: #606266;
	flex-shrink: 0;
}

.detail-value {
	flex: 1;
	color: #303133;
}

.detail-value.banned {
	color: #F56C6C;
}

.loading-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.8);
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid #f3f3f3;
	border-top: 4rpx solid #409EFF;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	margin-top: 20rpx;
	font-size: 28rpx;
	color: #666;
}

/* 图标字体样式 */
.icon-search::before { content: '🔍'; }
</style>
