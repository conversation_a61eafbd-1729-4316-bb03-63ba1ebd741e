/* 管理后台公共样式 */

/* 全局变量 */
:root {
  --primary-color: #409EFF;
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --info-color: #909399;
  
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --text-placeholder: #C0C4CC;
  
  --border-base: #DCDFE6;
  --border-light: #E4E7ED;
  --border-lighter: #EBEEF5;
  --border-extra-light: #F2F6FC;
  
  --background-base: #F5F7FA;
  --background-light: #FAFAFA;
  --background-white: #FFFFFF;
  
  --box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  
  --border-radius-base: 4px;
  --border-radius-small: 2px;
  --border-radius-large: 8px;
  
  --font-size-extra-large: 20px;
  --font-size-large: 18px;
  --font-size-medium: 16px;
  --font-size-base: 14px;
  --font-size-small: 13px;
  --font-size-extra-small: 12px;
}

/* 重置样式 */
* {
  box-sizing: border-box;
}

page {
  background-color: var(--background-base);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: var(--font-size-base);
  color: var(--text-primary);
  line-height: 1.5;
}

/* 布局类 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20rpx;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-1 {
  flex: 1;
}

/* 间距类 */
.m-0 { margin: 0; }
.m-1 { margin: 10rpx; }
.m-2 { margin: 20rpx; }
.m-3 { margin: 30rpx; }
.m-4 { margin: 40rpx; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 10rpx; }
.mt-2 { margin-top: 20rpx; }
.mt-3 { margin-top: 30rpx; }
.mt-4 { margin-top: 40rpx; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 10rpx; }
.mb-2 { margin-bottom: 20rpx; }
.mb-3 { margin-bottom: 30rpx; }
.mb-4 { margin-bottom: 40rpx; }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: 10rpx; }
.ml-2 { margin-left: 20rpx; }
.ml-3 { margin-left: 30rpx; }
.ml-4 { margin-left: 40rpx; }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: 10rpx; }
.mr-2 { margin-right: 20rpx; }
.mr-3 { margin-right: 30rpx; }
.mr-4 { margin-right: 40rpx; }

.p-0 { padding: 0; }
.p-1 { padding: 10rpx; }
.p-2 { padding: 20rpx; }
.p-3 { padding: 30rpx; }
.p-4 { padding: 40rpx; }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: 10rpx; }
.pt-2 { padding-top: 20rpx; }
.pt-3 { padding-top: 30rpx; }
.pt-4 { padding-top: 40rpx; }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: 10rpx; }
.pb-2 { padding-bottom: 20rpx; }
.pb-3 { padding-bottom: 30rpx; }
.pb-4 { padding-bottom: 40rpx; }

.pl-0 { padding-left: 0; }
.pl-1 { padding-left: 10rpx; }
.pl-2 { padding-left: 20rpx; }
.pl-3 { padding-left: 30rpx; }
.pl-4 { padding-left: 40rpx; }

.pr-0 { padding-right: 0; }
.pr-1 { padding-right: 10rpx; }
.pr-2 { padding-right: 20rpx; }
.pr-3 { padding-right: 30rpx; }
.pr-4 { padding-right: 40rpx; }

/* 文本类 */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

.text-primary { color: var(--primary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }
.text-info { color: var(--info-color); }

.text-regular { color: var(--text-regular); }
.text-secondary { color: var(--text-secondary); }
.text-placeholder { color: var(--text-placeholder); }

.font-bold { font-weight: bold; }
.font-normal { font-weight: normal; }

.text-xs { font-size: 22rpx; }
.text-sm { font-size: 26rpx; }
.text-base { font-size: 28rpx; }
.text-lg { font-size: 32rpx; }
.text-xl { font-size: 36rpx; }
.text-2xl { font-size: 40rpx; }

/* 背景类 */
.bg-white { background-color: var(--background-white); }
.bg-light { background-color: var(--background-light); }
.bg-base { background-color: var(--background-base); }

.bg-primary { background-color: var(--primary-color); }
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-danger { background-color: var(--danger-color); }
.bg-info { background-color: var(--info-color); }

/* 边框类 */
.border { border: 1rpx solid var(--border-base); }
.border-light { border: 1rpx solid var(--border-light); }
.border-lighter { border: 1rpx solid var(--border-lighter); }

.border-t { border-top: 1rpx solid var(--border-base); }
.border-b { border-bottom: 1rpx solid var(--border-base); }
.border-l { border-left: 1rpx solid var(--border-base); }
.border-r { border-right: 1rpx solid var(--border-base); }

.rounded { border-radius: 8rpx; }
.rounded-sm { border-radius: 4rpx; }
.rounded-lg { border-radius: 16rpx; }
.rounded-full { border-radius: 50%; }

/* 阴影类 */
.shadow { box-shadow: var(--box-shadow-base); }
.shadow-light { box-shadow: var(--box-shadow-light); }
.shadow-none { box-shadow: none; }

/* 按钮类 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 40rpx;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  box-sizing: border-box;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-warning {
  background-color: var(--warning-color);
  color: white;
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
}

.btn-info {
  background-color: var(--info-color);
  color: white;
}

.btn-default {
  background-color: white;
  color: var(--text-primary);
  border: 1rpx solid var(--border-base);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-sm {
  padding: 15rpx 30rpx;
  font-size: 24rpx;
}

.btn-lg {
  padding: 25rpx 50rpx;
  font-size: 32rpx;
}

/* 卡片类 */
.card {
  background-color: white;
  border-radius: 16rpx;
  box-shadow: var(--box-shadow-base);
  overflow: hidden;
}

.card-header {
  padding: 30rpx;
  border-bottom: 1rpx solid var(--border-lighter);
}

.card-body {
  padding: 30rpx;
}

.card-footer {
  padding: 30rpx;
  border-top: 1rpx solid var(--border-lighter);
  background-color: var(--background-light);
}

/* 表单类 */
.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  margin-bottom: 10rpx;
  font-size: 28rpx;
  color: var(--text-regular);
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid var(--border-light);
  border-radius: 8rpx;
  font-size: 28rpx;
  color: var(--text-primary);
  background-color: white;
  transition: border-color 0.3s;
}

.form-input:focus {
  border-color: var(--primary-color);
  outline: none;
}

.form-textarea {
  width: 100%;
  min-height: 160rpx;
  padding: 20rpx;
  border: 2rpx solid var(--border-light);
  border-radius: 8rpx;
  font-size: 28rpx;
  color: var(--text-primary);
  background-color: white;
  resize: vertical;
  transition: border-color 0.3s;
}

.form-textarea:focus {
  border-color: var(--primary-color);
  outline: none;
}

/* 状态类 */
.status-pending {
  color: var(--warning-color);
}

.status-approved {
  color: var(--success-color);
}

.status-rejected {
  color: var(--danger-color);
}

.status-banned {
  color: var(--danger-color);
}

.status-active {
  color: var(--success-color);
}

/* 徽章类 */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 5rpx 15rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  font-weight: 500;
  color: white;
}

.badge-primary { background-color: var(--primary-color); }
.badge-success { background-color: var(--success-color); }
.badge-warning { background-color: var(--warning-color); }
.badge-danger { background-color: var(--danger-color); }
.badge-info { background-color: var(--info-color); }

/* 加载动画 */
.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 工具类 */
.hidden { display: none !important; }
.visible { visibility: visible; }
.invisible { visibility: hidden; }

.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }

.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }

.select-none { user-select: none; }

/* 响应式类 */
@media (max-width: 768px) {
  .hidden-mobile { display: none !important; }
}

@media (min-width: 769px) {
  .hidden-desktop { display: none !important; }
}

/* 动画类 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.scale-in {
  animation: scaleIn 0.3s ease-out;
}

@keyframes scaleIn {
  from {
    transform: scale(0.8);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}
