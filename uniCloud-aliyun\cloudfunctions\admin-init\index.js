'use strict';

const uniID = require('uni-id-common');

exports.main = async (event, context) => {
  console.log('开始初始化管理员账号...');
  
  const db = uniCloud.database();
  const userCollection = db.collection('user');
  
  try {
    // 检查是否已存在管理员账号
    const existingAdmin = await userCollection.where({
      username: 'admin'
    }).get();
    
    if (existingAdmin.data.length > 0) {
      console.log('管理员账号已存在');
      return {
        code: 200,
        message: '管理员账号已存在',
        data: {
          username: 'admin',
          message: '账号已存在，可以直接登录'
        }
      };
    }
    
    // 创建管理员账号
    console.log('创建管理员账号...');
    
    const registerResult = await uniID.createUser({
      username: 'admin',
      password: 'admin123',
      nickname: '系统管理员',
      role: ['admin']
    });
    
    if (registerResult.code === 0) {
      console.log('管理员账号创建成功，用户ID:', registerResult.uid);
      
      return {
        code: 200,
        message: '管理员账号创建成功',
        data: {
          username: 'admin',
          password: 'admin123',
          uid: registerResult.uid,
          message: '请使用 admin/admin123 登录管理后台'
        }
      };
    } else {
      console.error('创建管理员账号失败:', registerResult);
      return {
        code: 500,
        message: '创建管理员账号失败',
        error: registerResult.msg
      };
    }
    
  } catch (error) {
    console.error('初始化管理员账号时发生错误:', error);
    return {
      code: 500,
      message: '初始化管理员账号失败',
      error: error.message
    };
  }
};
