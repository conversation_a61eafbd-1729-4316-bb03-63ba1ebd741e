'use strict';

const crypto = require('crypto');

// 管理员账号配置（实际项目中应该存储在数据库中）
const ADMIN_ACCOUNTS = {
  'admin': 'e3afed0047b08059d0fada10f400c1e5' // admin123 的 MD5
};

// 密码加密
function hashPassword(password) {
  return crypto.createHash('md5').update(password).digest('hex');
}

// 生成管理员token
function generateAdminToken(username) {
  const payload = {
    username: username,
    role: 'admin',
    timestamp: Date.now()
  };
  return Buffer.from(JSON.stringify(payload)).toString('base64');
}

// 验证管理员token
function verifyAdminToken(token) {
  try {
    const payload = JSON.parse(Buffer.from(token, 'base64').toString());
    if (payload.role !== 'admin') {
      return null;
    }
    // token有效期24小时
    if (Date.now() - payload.timestamp > 24 * 60 * 60 * 1000) {
      return null;
    }
    return payload.username;
  } catch (error) {
    return null;
  }
}

exports.main = async (event, context) => {
  const { action, data } = event;
  const db = uniCloud.database();
  const houseCollection = db.collection('house');
  const userCollection = db.collection('user');
  
  try {
    switch (action) {
      case 'adminLogin':
        return await adminLogin(data);
      case 'getHouseList':
        return await getHouseList(houseCollection, data);
      case 'auditHouse':
        return await auditHouse(houseCollection, data);
      case 'deleteHouse':
        return await deleteHouse(houseCollection, data);
      case 'getUserList':
        return await getUserList(userCollection, data);
      case 'banUser':
        return await banUser(userCollection, data);
      case 'unbanUser':
        return await unbanUser(userCollection, data);
      case 'getStats':
        return await getStats(houseCollection, userCollection, data);
      default:
        return {
          code: 400,
          message: '无效的操作类型'
        };
    }
  } catch (error) {
    console.error('管理员云函数错误:', error);
    return {
      code: 500,
      message: '服务器内部错误',
      error: error.message
    };
  }
};

// 管理员登录
async function adminLogin(data) {
  const { username, password } = data;
  
  if (!username || !password) {
    return {
      code: 400,
      message: '用户名和密码不能为空'
    };
  }
  
  const hashedPassword = hashPassword(password);
  
  if (!ADMIN_ACCOUNTS[username] || ADMIN_ACCOUNTS[username] !== hashedPassword) {
    return {
      code: 400,
      message: '用户名或密码错误'
    };
  }
  
  const token = generateAdminToken(username);
  
  return {
    code: 200,
    message: '登录成功',
    data: {
      username,
      token
    }
  };
}

// 获取房源列表（管理员）
async function getHouseList(houseCollection, data) {
  const { token, page = 1, pageSize = 10, status, keyword } = data;
  
  if (!token) {
    return {
      code: 401,
      message: '请先登录'
    };
  }
  
  const adminUser = verifyAdminToken(token);
  if (!adminUser) {
    return {
      code: 401,
      message: '管理员token无效或已过期'
    };
  }
  
  let query = houseCollection;
  
  // 状态筛选
  if (status) {
    query = query.where({ status });
  }
  
  // 关键词搜索
  if (keyword) {
    query = query.where({
      $or: [
        { title: new RegExp(keyword, 'i') },
        { desc: new RegExp(keyword, 'i') },
        { 'location.address': new RegExp(keyword, 'i') }
      ]
    });
  }
  
  // 分页
  const skip = (page - 1) * pageSize;
  const result = await query
    .orderBy('created_at', 'desc')
    .skip(skip)
    .limit(pageSize)
    .get();
  
  // 获取总数
  let countQuery = houseCollection;
  if (status) {
    countQuery = countQuery.where({ status });
  }
  const countResult = await countQuery.count();
  
  return {
    code: 200,
    message: '获取成功',
    data: {
      list: result.data,
      total: countResult.total,
      page,
      pageSize,
      totalPages: Math.ceil(countResult.total / pageSize)
    }
  };
}

// 审核房源
async function auditHouse(houseCollection, data) {
  const { token, houseId, status, reason } = data;
  
  if (!token) {
    return {
      code: 401,
      message: '请先登录'
    };
  }
  
  const adminUser = verifyAdminToken(token);
  if (!adminUser) {
    return {
      code: 401,
      message: '管理员token无效或已过期'
    };
  }
  
  if (!houseId || !status) {
    return {
      code: 400,
      message: '房源ID和审核状态不能为空'
    };
  }
  
  if (!['approved', 'rejected'].includes(status)) {
    return {
      code: 400,
      message: '无效的审核状态'
    };
  }
  
  // 检查房源是否存在
  const houseResult = await houseCollection.doc(houseId).get();
  if (houseResult.data.length === 0) {
    return {
      code: 404,
      message: '房源不存在'
    };
  }
  
  const updateData = {
    status,
    updated_at: new Date(),
    audit_time: new Date(),
    audit_by: adminUser
  };
  
  if (status === 'rejected' && reason) {
    updateData.reject_reason = reason;
  }
  
  const result = await houseCollection.doc(houseId).update(updateData);
  
  if (result.updated > 0) {
    return {
      code: 200,
      message: status === 'approved' ? '审核通过' : '审核驳回'
    };
  } else {
    return {
      code: 500,
      message: '审核失败'
    };
  }
}

// 删除房源（管理员）
async function deleteHouse(houseCollection, data) {
  const { token, houseId } = data;
  
  if (!token) {
    return {
      code: 401,
      message: '请先登录'
    };
  }
  
  const adminUser = verifyAdminToken(token);
  if (!adminUser) {
    return {
      code: 401,
      message: '管理员token无效或已过期'
    };
  }
  
  if (!houseId) {
    return {
      code: 400,
      message: '房源ID不能为空'
    };
  }
  
  const result = await houseCollection.doc(houseId).remove();
  
  if (result.deleted > 0) {
    return {
      code: 200,
      message: '删除成功'
    };
  } else {
    return {
      code: 404,
      message: '房源不存在或删除失败'
    };
  }
}

// 获取用户列表
async function getUserList(userCollection, data) {
  const { token, page = 1, pageSize = 10, keyword } = data;
  
  if (!token) {
    return {
      code: 401,
      message: '请先登录'
    };
  }
  
  const adminUser = verifyAdminToken(token);
  if (!adminUser) {
    return {
      code: 401,
      message: '管理员token无效或已过期'
    };
  }
  
  let query = userCollection;
  
  // 关键词搜索
  if (keyword) {
    query = query.where({
      $or: [
        { username: new RegExp(keyword, 'i') },
        { nickname: new RegExp(keyword, 'i') },
        { phone: new RegExp(keyword, 'i') }
      ]
    });
  }
  
  // 分页
  const skip = (page - 1) * pageSize;
  const result = await query
    .field({ password: false }) // 不返回密码字段
    .orderBy('created_at', 'desc')
    .skip(skip)
    .limit(pageSize)
    .get();
  
  // 获取总数
  let countQuery = userCollection;
  if (keyword) {
    countQuery = countQuery.where({
      $or: [
        { username: new RegExp(keyword, 'i') },
        { nickname: new RegExp(keyword, 'i') },
        { phone: new RegExp(keyword, 'i') }
      ]
    });
  }
  const countResult = await countQuery.count();
  
  return {
    code: 200,
    message: '获取成功',
    data: {
      list: result.data,
      total: countResult.total,
      page,
      pageSize,
      totalPages: Math.ceil(countResult.total / pageSize)
    }
  };
}

// 封禁用户
async function banUser(userCollection, data) {
  const { token, userId } = data;
  
  if (!token) {
    return {
      code: 401,
      message: '请先登录'
    };
  }
  
  const adminUser = verifyAdminToken(token);
  if (!adminUser) {
    return {
      code: 401,
      message: '管理员token无效或已过期'
    };
  }
  
  if (!userId) {
    return {
      code: 400,
      message: '用户ID不能为空'
    };
  }
  
  const result = await userCollection.doc(userId).update({
    is_banned: true
  });
  
  if (result.updated > 0) {
    return {
      code: 200,
      message: '封禁成功'
    };
  } else {
    return {
      code: 404,
      message: '用户不存在或封禁失败'
    };
  }
}

// 解封用户
async function unbanUser(userCollection, data) {
  const { token, userId } = data;
  
  if (!token) {
    return {
      code: 401,
      message: '请先登录'
    };
  }
  
  const adminUser = verifyAdminToken(token);
  if (!adminUser) {
    return {
      code: 401,
      message: '管理员token无效或已过期'
    };
  }
  
  if (!userId) {
    return {
      code: 400,
      message: '用户ID不能为空'
    };
  }
  
  const result = await userCollection.doc(userId).update({
    is_banned: false
  });
  
  if (result.updated > 0) {
    return {
      code: 200,
      message: '解封成功'
    };
  } else {
    return {
      code: 404,
      message: '用户不存在或解封失败'
    };
  }
}

// 获取统计数据
async function getStats(houseCollection, userCollection, data) {
  const { token } = data;
  
  if (!token) {
    return {
      code: 401,
      message: '请先登录'
    };
  }
  
  const adminUser = verifyAdminToken(token);
  if (!adminUser) {
    return {
      code: 401,
      message: '管理员token无效或已过期'
    };
  }
  
  // 获取房源统计
  const totalHouses = await houseCollection.count();
  const approvedHouses = await houseCollection.where({ status: 'approved' }).count();
  const pendingHouses = await houseCollection.where({ status: 'pending' }).count();
  const rejectedHouses = await houseCollection.where({ status: 'rejected' }).count();
  
  // 获取用户统计
  const totalUsers = await userCollection.count();
  const bannedUsers = await userCollection.where({ is_banned: true }).count();
  
  return {
    code: 200,
    message: '获取成功',
    data: {
      houses: {
        total: totalHouses.total,
        approved: approvedHouses.total,
        pending: pendingHouses.total,
        rejected: rejectedHouses.total
      },
      users: {
        total: totalUsers.total,
        banned: bannedUsers.total,
        active: totalUsers.total - bannedUsers.total
      }
    }
  };
}
