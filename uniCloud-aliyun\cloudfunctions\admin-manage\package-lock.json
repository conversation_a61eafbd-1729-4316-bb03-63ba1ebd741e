{"name": "admin-manage", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "admin-manage", "version": "1.0.0", "license": "ISC", "dependencies": {"crypto": "^1.0.1"}}, "node_modules/crypto": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/crypto/-/crypto-1.0.1.tgz", "integrity": "sha512-VxBKmeNcqQdiUQUW2Tzq0t377b54N2bMtXO/qiLa+6eRRmmC4qT3D4OnTGoT/U6O9aklQ/jTwbOtRMTTY8G0Ig==", "deprecated": "This package is no longer supported. It's now a built-in Node module. If you've depended on crypto, you should switch to the one that's built-in."}}}