const db = uniCloud.database();
const dbCmd = db.command;

class AdminController {
  // 获取用户列表
  async getUserList(event, context) {
    try {
      const { page = 1, limit = 10, keyword, is_banned } = event;
      
      const skip = (page - 1) * limit;
      
      let whereCondition = {};
      
      // 关键词搜索
      if (keyword) {
        whereCondition[dbCmd.or] = [
          { username: new RegExp(keyword, 'i') },
          { nickname: new RegExp(keyword, 'i') },
          { phone: new RegExp(keyword, 'i') },
          { email: new RegExp(keyword, 'i') }
        ];
      }
      
      // 封禁状态筛选
      if (is_banned !== undefined) {
        whereCondition.is_banned = is_banned;
      }

      const result = await db.collection('user')
        .where(whereCondition)
        .field({
          password: false // 不返回密码字段
        })
        .orderBy('created_at', 'desc')
        .skip(skip)
        .limit(limit)
        .get();
      
      const countResult = await db.collection('user').where(whereCondition).count();
      
      return {
        code: 0,
        message: '获取成功',
        data: {
          list: result.data,
          total: countResult.total,
          page,
          limit,
          totalPages: Math.ceil(countResult.total / limit)
        }
      };
    } catch (error) {
      console.error('Get user list error:', error);
      return {
        code: 500,
        message: '获取用户列表失败',
        error: error.message
      };
    }
  }

  // 封禁用户
  async banUser(event, context) {
    try {
      const { params } = event;
      const userId = params.id;
      
      const userDoc = await db.collection('user').doc(userId).get();
      
      if (!userDoc.data.length) {
        return {
          code: 404,
          message: '用户不存在'
        };
      }
      
      const user = userDoc.data[0];
      
      if (user.role && user.role.includes('admin')) {
        return {
          code: 400,
          message: '不能封禁管理员账号'
        };
      }
      
      if (user.is_banned) {
        return {
          code: 400,
          message: '用户已被封禁'
        };
      }

      await db.collection('user').doc(userId).update({
        is_banned: true,
        updated_at: new Date()
      });
      
      return {
        code: 0,
        message: '用户封禁成功'
      };
    } catch (error) {
      console.error('Ban user error:', error);
      return {
        code: 500,
        message: '封禁用户失败',
        error: error.message
      };
    }
  }

  // 解封用户
  async unbanUser(event, context) {
    try {
      const { params } = event;
      const userId = params.id;
      
      const userDoc = await db.collection('user').doc(userId).get();
      
      if (!userDoc.data.length) {
        return {
          code: 404,
          message: '用户不存在'
        };
      }
      
      const user = userDoc.data[0];
      
      if (!user.is_banned) {
        return {
          code: 400,
          message: '用户未被封禁'
        };
      }

      await db.collection('user').doc(userId).update({
        is_banned: false,
        updated_at: new Date()
      });
      
      return {
        code: 0,
        message: '用户解封成功'
      };
    } catch (error) {
      console.error('Unban user error:', error);
      return {
        code: 500,
        message: '解封用户失败',
        error: error.message
      };
    }
  }

  // 获取房源列表（管理员）
  async getHouseList(event, context) {
    try {
      const { page = 1, limit = 10, status, keyword, owner_id } = event;
      
      const skip = (page - 1) * limit;
      
      let whereCondition = {};
      
      // 状态筛选
      if (status) {
        whereCondition.status = status;
      }
      
      // 关键词搜索
      if (keyword) {
        whereCondition[dbCmd.or] = [
          { title: new RegExp(keyword, 'i') },
          { desc: new RegExp(keyword, 'i') },
          { 'location.address': new RegExp(keyword, 'i') }
        ];
      }
      
      // 房源所有者筛选
      if (owner_id) {
        whereCondition.owner_id = owner_id;
      }

      const result = await db.collection('house')
        .where(whereCondition)
        .orderBy('created_at', 'desc')
        .skip(skip)
        .limit(limit)
        .get();
      
      // 获取房源所有者信息
      const ownerIds = [...new Set(result.data.map(house => house.owner_id))];
      const ownerResult = await db.collection('user')
        .where({
          _id: dbCmd.in(ownerIds)
        })
        .field({
          _id: true,
          username: true,
          nickname: true,
          phone: true
        })
        .get();
      
      // 合并数据
      const list = result.data.map(house => {
        const owner = ownerResult.data.find(u => u._id === house.owner_id);
        return {
          ...house,
          owner_info: owner
        };
      });
      
      const countResult = await db.collection('house').where(whereCondition).count();
      
      return {
        code: 0,
        message: '获取成功',
        data: {
          list,
          total: countResult.total,
          page,
          limit,
          totalPages: Math.ceil(countResult.total / limit)
        }
      };
    } catch (error) {
      console.error('Get house list error:', error);
      return {
        code: 500,
        message: '获取房源列表失败',
        error: error.message
      };
    }
  }

  // 审核通过房源
  async approveHouse(event, context) {
    try {
      const { params } = event;
      const houseId = params.id;
      
      const houseDoc = await db.collection('house').doc(houseId).get();
      
      if (!houseDoc.data.length) {
        return {
          code: 404,
          message: '房源不存在'
        };
      }
      
      const house = houseDoc.data[0];
      
      if (house.status === 'approved') {
        return {
          code: 400,
          message: '房源已审核通过'
        };
      }

      await db.collection('house').doc(houseId).update({
        status: 'approved',
        approved_at: new Date(),
        updated_at: new Date(),
        reject_reason: dbCmd.remove() // 清除驳回原因
      });
      
      return {
        code: 0,
        message: '房源审核通过'
      };
    } catch (error) {
      console.error('Approve house error:', error);
      return {
        code: 500,
        message: '审核房源失败',
        error: error.message
      };
    }
  }

  // 驳回房源
  async rejectHouse(event, context) {
    try {
      const { params, reason } = event;
      const houseId = params.id;
      
      if (!reason) {
        return {
          code: 400,
          message: '驳回原因不能为空'
        };
      }
      
      const houseDoc = await db.collection('house').doc(houseId).get();
      
      if (!houseDoc.data.length) {
        return {
          code: 404,
          message: '房源不存在'
        };
      }
      
      const house = houseDoc.data[0];
      
      if (house.status === 'rejected') {
        return {
          code: 400,
          message: '房源已被驳回'
        };
      }

      await db.collection('house').doc(houseId).update({
        status: 'rejected',
        reject_reason: reason,
        updated_at: new Date(),
        approved_at: dbCmd.remove() // 清除审核通过时间
      });
      
      return {
        code: 0,
        message: '房源已驳回'
      };
    } catch (error) {
      console.error('Reject house error:', error);
      return {
        code: 500,
        message: '驳回房源失败',
        error: error.message
      };
    }
  }

  // 获取统计数据
  async getStatistics(event, context) {
    try {
      // 用户统计
      const totalUsers = await db.collection('user').count();
      const bannedUsers = await db.collection('user').where({
        is_banned: true
      }).count();

      // 房源统计
      const totalHouses = await db.collection('house').count();
      const pendingHouses = await db.collection('house').where({
        status: 'pending'
      }).count();
      const approvedHouses = await db.collection('house').where({
        status: 'approved'
      }).count();
      const rejectedHouses = await db.collection('house').where({
        status: 'rejected'
      }).count();

      // 收藏统计
      const totalFavorites = await db.collection('user_favorites').count();

      // 今日新增统计
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const todayUsers = await db.collection('user').where({
        created_at: dbCmd.gte(today)
      }).count();

      const todayHouses = await db.collection('house').where({
        created_at: dbCmd.gte(today)
      }).count();

      // 本月统计
      const thisMonth = new Date();
      thisMonth.setDate(1);
      thisMonth.setHours(0, 0, 0, 0);

      const monthUsers = await db.collection('user').where({
        created_at: dbCmd.gte(thisMonth)
      }).count();

      const monthHouses = await db.collection('house').where({
        created_at: dbCmd.gte(thisMonth)
      }).count();

      return {
        code: 0,
        message: '获取成功',
        data: {
          users: {
            total: totalUsers.total,
            banned: bannedUsers.total,
            active: totalUsers.total - bannedUsers.total,
            today: todayUsers.total,
            thisMonth: monthUsers.total
          },
          houses: {
            total: totalHouses.total,
            pending: pendingHouses.total,
            approved: approvedHouses.total,
            rejected: rejectedHouses.total,
            today: todayHouses.total,
            thisMonth: monthHouses.total
          },
          favorites: {
            total: totalFavorites.total
          }
        }
      };
    } catch (error) {
      console.error('Get statistics error:', error);
      return {
        code: 500,
        message: '获取统计数据失败',
        error: error.message
      };
    }
  }

  // 获取系统配置
  async getSystemConfig(event, context) {
    try {
      const result = await db.collection('system_config').get();

      const config = {};
      result.data.forEach(item => {
        config[item.key] = item.value;
      });

      return {
        code: 0,
        message: '获取成功',
        data: config
      };
    } catch (error) {
      console.error('Get system config error:', error);
      return {
        code: 500,
        message: '获取系统配置失败',
        error: error.message
      };
    }
  }

  // 更新系统配置
  async updateSystemConfig(event, context) {
    try {
      const { key, value, description } = event;

      if (!key || value === undefined) {
        return {
          code: 400,
          message: '配置键和值不能为空'
        };
      }

      const validKeys = ['banner_list', 'announcement', 'search_keywords', 'contact_info'];
      if (!validKeys.includes(key)) {
        return {
          code: 400,
          message: '无效的配置键'
        };
      }

      // 检查配置是否存在
      const existConfig = await db.collection('system_config').where({
        key
      }).get();

      if (existConfig.data.length > 0) {
        // 更新现有配置
        await db.collection('system_config').doc(existConfig.data[0]._id).update({
          value,
          description: description || existConfig.data[0].description,
          updated_at: new Date()
        });
      } else {
        // 创建新配置
        await db.collection('system_config').add({
          key,
          value,
          description: description || '',
          updated_at: new Date()
        });
      }

      return {
        code: 0,
        message: '配置更新成功'
      };
    } catch (error) {
      console.error('Update system config error:', error);
      return {
        code: 500,
        message: '更新系统配置失败',
        error: error.message
      };
    }
  }
}

module.exports = new AdminController();
