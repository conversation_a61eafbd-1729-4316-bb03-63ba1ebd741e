const db = uniCloud.database();
const dbCmd = db.command;

class HouseController {
  // 创建房源
  async createHouse(event, context) {
    try {
      const { userInfo, title, desc, images, location, price, type, config, contact, area, floor, orientation, is_furnished } = event;
      
      // 参数验证
      if (!title || !desc || !location || !price || !type) {
        return {
          code: 400,
          message: '标题、描述、位置、价格和房型不能为空'
        };
      }
      
      if (title.length > 100) {
        return {
          code: 400,
          message: '标题长度不能超过100个字符'
        };
      }
      
      if (desc.length > 1000) {
        return {
          code: 400,
          message: '描述长度不能超过1000个字符'
        };
      }
      
      if (price <= 0) {
        return {
          code: 400,
          message: '价格必须大于0'
        };
      }
      
      if (!location.address) {
        return {
          code: 400,
          message: '详细地址不能为空'
        };
      }

      const houseData = {
        title,
        desc,
        images: images || [],
        location,
        price,
        type,
        config: config || [],
        contact: contact || {},
        owner_id: userInfo.uid,
        status: 'pending',
        area: area || 0,
        floor: floor || '',
        orientation: orientation || '',
        is_furnished: is_furnished || false,
        view_count: 0,
        favorite_count: 0,
        created_at: new Date(),
        updated_at: new Date()
      };

      const result = await db.collection('house').add(houseData);
      
      return {
        code: 0,
        message: '房源发布成功，等待审核',
        data: {
          id: result.id
        }
      };
    } catch (error) {
      console.error('Create house error:', error);
      return {
        code: 500,
        message: '发布房源失败',
        error: error.message
      };
    }
  }

  // 更新房源
  async updateHouse(event, context) {
    try {
      const { userInfo, params, title, desc, images, location, price, type, config, contact, area, floor, orientation, is_furnished } = event;
      const houseId = params.id;
      
      // 检查房源是否存在且属于当前用户
      const houseDoc = await db.collection('house').doc(houseId).get();
      
      if (!houseDoc.data.length) {
        return {
          code: 404,
          message: '房源不存在'
        };
      }
      
      const house = houseDoc.data[0];
      
      // 检查权限（只有房源所有者或管理员可以修改）
      if (house.owner_id !== userInfo.uid && !userInfo.role.includes('admin')) {
        return {
          code: 403,
          message: '无权修改此房源'
        };
      }

      const updateData = {
        updated_at: new Date()
      };
      
      // 如果是普通用户修改，需要重新审核
      if (house.owner_id === userInfo.uid && !userInfo.role.includes('admin')) {
        updateData.status = 'pending';
      }
      
      if (title !== undefined) {
        if (title.length > 100) {
          return {
            code: 400,
            message: '标题长度不能超过100个字符'
          };
        }
        updateData.title = title;
      }
      
      if (desc !== undefined) {
        if (desc.length > 1000) {
          return {
            code: 400,
            message: '描述长度不能超过1000个字符'
          };
        }
        updateData.desc = desc;
      }
      
      if (images !== undefined) {
        updateData.images = images;
      }
      
      if (location !== undefined) {
        if (!location.address) {
          return {
            code: 400,
            message: '详细地址不能为空'
          };
        }
        updateData.location = location;
      }
      
      if (price !== undefined) {
        if (price <= 0) {
          return {
            code: 400,
            message: '价格必须大于0'
          };
        }
        updateData.price = price;
      }
      
      if (type !== undefined) {
        updateData.type = type;
      }
      
      if (config !== undefined) {
        updateData.config = config;
      }
      
      if (contact !== undefined) {
        updateData.contact = contact;
      }
      
      if (area !== undefined) {
        updateData.area = area;
      }
      
      if (floor !== undefined) {
        updateData.floor = floor;
      }
      
      if (orientation !== undefined) {
        updateData.orientation = orientation;
      }
      
      if (is_furnished !== undefined) {
        updateData.is_furnished = is_furnished;
      }

      await db.collection('house').doc(houseId).update(updateData);
      
      return {
        code: 0,
        message: '房源更新成功'
      };
    } catch (error) {
      console.error('Update house error:', error);
      return {
        code: 500,
        message: '更新房源失败',
        error: error.message
      };
    }
  }

  // 删除房源
  async deleteHouse(event, context) {
    try {
      const { userInfo, params } = event;
      const houseId = params.id;
      
      // 检查房源是否存在且属于当前用户
      const houseDoc = await db.collection('house').doc(houseId).get();
      
      if (!houseDoc.data.length) {
        return {
          code: 404,
          message: '房源不存在'
        };
      }
      
      const house = houseDoc.data[0];
      
      // 检查权限（只有房源所有者或管理员可以删除）
      if (house.owner_id !== userInfo.uid && !userInfo.role.includes('admin')) {
        return {
          code: 403,
          message: '无权删除此房源'
        };
      }

      // 删除房源
      await db.collection('house').doc(houseId).remove();
      
      // 删除相关的收藏记录
      await db.collection('user_favorites').where({
        house_id: houseId
      }).remove();
      
      return {
        code: 0,
        message: '房源删除成功'
      };
    } catch (error) {
      console.error('Delete house error:', error);
      return {
        code: 500,
        message: '删除房源失败',
        error: error.message
      };
    }
  }

  // 获取房源列表
  async getHouseList(event, context) {
    try {
      const { 
        page = 1, 
        limit = 10, 
        status = 'approved',
        district,
        type,
        minPrice,
        maxPrice,
        config,
        sortBy = 'created_at',
        sortOrder = 'desc'
      } = event;
      
      const skip = (page - 1) * limit;
      
      // 构建查询条件
      let whereCondition = {
        status
      };
      
      if (district) {
        whereCondition['location.district'] = district;
      }
      
      if (type) {
        whereCondition.type = type;
      }
      
      if (minPrice || maxPrice) {
        whereCondition.price = {};
        if (minPrice) {
          whereCondition.price[dbCmd.gte] = minPrice;
        }
        if (maxPrice) {
          whereCondition.price[dbCmd.lte] = maxPrice;
        }
      }
      
      if (config && config.length > 0) {
        whereCondition.config = dbCmd.all(config);
      }

      // 查询房源列表
      const query = db.collection('house').where(whereCondition);
      
      // 排序
      const sortDirection = sortOrder === 'desc' ? 'desc' : 'asc';
      query.orderBy(sortBy, sortDirection);
      
      // 分页
      const result = await query.skip(skip).limit(limit).get();
      
      // 获取总数
      const countResult = await db.collection('house').where(whereCondition).count();
      
      return {
        code: 0,
        message: '获取成功',
        data: {
          list: result.data,
          total: countResult.total,
          page,
          limit,
          totalPages: Math.ceil(countResult.total / limit)
        }
      };
    } catch (error) {
      console.error('Get house list error:', error);
      return {
        code: 500,
        message: '获取房源列表失败',
        error: error.message
      };
    }
  }

  // 获取房源详情
  async getHouseDetail(event, context) {
    try {
      const { params } = event;
      const houseId = params.id;
      
      const houseDoc = await db.collection('house').doc(houseId).get();
      
      if (!houseDoc.data.length) {
        return {
          code: 404,
          message: '房源不存在'
        };
      }
      
      const house = houseDoc.data[0];
      
      // 只有已审核通过的房源才能被查看详情（管理员除外）
      if (house.status !== 'approved') {
        return {
          code: 403,
          message: '房源暂不可查看'
        };
      }
      
      // 增加浏览次数
      await db.collection('house').doc(houseId).update({
        view_count: dbCmd.inc(1)
      });
      
      // 获取房源所有者信息（脱敏）
      const ownerDoc = await db.collection('user').doc(house.owner_id).field({
        nickname: true,
        avatar: true
      }).get();
      
      if (ownerDoc.data.length > 0) {
        house.owner_info = ownerDoc.data[0];
      }
      
      return {
        code: 0,
        message: '获取成功',
        data: house
      };
    } catch (error) {
      console.error('Get house detail error:', error);
      return {
        code: 500,
        message: '获取房源详情失败',
        error: error.message
      };
    }
  }

  // 搜索房源
  async searchHouses(event, context) {
    try {
      const {
        keyword = '',
        page = 1,
        limit = 10,
        district,
        type,
        minPrice,
        maxPrice,
        config
      } = event;

      const skip = (page - 1) * limit;

      // 构建查询条件
      let whereCondition = {
        status: 'approved'
      };

      // 关键词搜索
      if (keyword) {
        whereCondition[dbCmd.or] = [
          { title: new RegExp(keyword, 'i') },
          { desc: new RegExp(keyword, 'i') },
          { 'location.address': new RegExp(keyword, 'i') }
        ];
      }

      if (district) {
        whereCondition['location.district'] = district;
      }

      if (type) {
        whereCondition.type = type;
      }

      if (minPrice || maxPrice) {
        whereCondition.price = {};
        if (minPrice) {
          whereCondition.price[dbCmd.gte] = minPrice;
        }
        if (maxPrice) {
          whereCondition.price[dbCmd.lte] = maxPrice;
        }
      }

      if (config && config.length > 0) {
        whereCondition.config = dbCmd.all(config);
      }

      // 查询房源列表
      const result = await db.collection('house')
        .where(whereCondition)
        .orderBy('created_at', 'desc')
        .skip(skip)
        .limit(limit)
        .get();

      // 获取总数
      const countResult = await db.collection('house').where(whereCondition).count();

      return {
        code: 0,
        message: '搜索成功',
        data: {
          list: result.data,
          total: countResult.total,
          page,
          limit,
          totalPages: Math.ceil(countResult.total / limit)
        }
      };
    } catch (error) {
      console.error('Search houses error:', error);
      return {
        code: 500,
        message: '搜索房源失败',
        error: error.message
      };
    }
  }

  // 获取我的房源
  async getMyHouses(event, context) {
    try {
      const { userInfo, page = 1, limit = 10, status } = event;

      const skip = (page - 1) * limit;

      let whereCondition = {
        owner_id: userInfo.uid
      };

      if (status) {
        whereCondition.status = status;
      }

      const result = await db.collection('house')
        .where(whereCondition)
        .orderBy('created_at', 'desc')
        .skip(skip)
        .limit(limit)
        .get();

      const countResult = await db.collection('house').where(whereCondition).count();

      return {
        code: 0,
        message: '获取成功',
        data: {
          list: result.data,
          total: countResult.total,
          page,
          limit,
          totalPages: Math.ceil(countResult.total / limit)
        }
      };
    } catch (error) {
      console.error('Get my houses error:', error);
      return {
        code: 500,
        message: '获取我的房源失败',
        error: error.message
      };
    }
  }

  // 添加收藏
  async addFavorite(event, context) {
    try {
      const { userInfo, house_id } = event;

      if (!house_id) {
        return {
          code: 400,
          message: '房源ID不能为空'
        };
      }

      // 检查房源是否存在
      const houseDoc = await db.collection('house').doc(house_id).get();

      if (!houseDoc.data.length) {
        return {
          code: 404,
          message: '房源不存在'
        };
      }

      // 检查是否已收藏
      const existFavorite = await db.collection('user_favorites').where({
        user_id: userInfo.uid,
        house_id
      }).get();

      if (existFavorite.data.length > 0) {
        return {
          code: 400,
          message: '已收藏该房源'
        };
      }

      // 添加收藏
      await db.collection('user_favorites').add({
        user_id: userInfo.uid,
        house_id,
        created_at: new Date()
      });

      // 更新房源收藏数
      await db.collection('house').doc(house_id).update({
        favorite_count: dbCmd.inc(1)
      });

      return {
        code: 0,
        message: '收藏成功'
      };
    } catch (error) {
      console.error('Add favorite error:', error);
      return {
        code: 500,
        message: '收藏失败',
        error: error.message
      };
    }
  }

  // 取消收藏
  async removeFavorite(event, context) {
    try {
      const { userInfo, house_id } = event;

      if (!house_id) {
        return {
          code: 400,
          message: '房源ID不能为空'
        };
      }

      // 查找收藏记录
      const favoriteDoc = await db.collection('user_favorites').where({
        user_id: userInfo.uid,
        house_id
      }).get();

      if (!favoriteDoc.data.length) {
        return {
          code: 400,
          message: '未收藏该房源'
        };
      }

      // 删除收藏记录
      await db.collection('user_favorites').doc(favoriteDoc.data[0]._id).remove();

      // 更新房源收藏数
      await db.collection('house').doc(house_id).update({
        favorite_count: dbCmd.inc(-1)
      });

      return {
        code: 0,
        message: '取消收藏成功'
      };
    } catch (error) {
      console.error('Remove favorite error:', error);
      return {
        code: 500,
        message: '取消收藏失败',
        error: error.message
      };
    }
  }

  // 获取收藏列表
  async getFavoriteList(event, context) {
    try {
      const { userInfo, page = 1, limit = 10 } = event;

      const skip = (page - 1) * limit;

      // 获取收藏记录
      const favoriteResult = await db.collection('user_favorites')
        .where({
          user_id: userInfo.uid
        })
        .orderBy('created_at', 'desc')
        .skip(skip)
        .limit(limit)
        .get();

      if (favoriteResult.data.length === 0) {
        return {
          code: 0,
          message: '获取成功',
          data: {
            list: [],
            total: 0,
            page,
            limit,
            totalPages: 0
          }
        };
      }

      // 获取房源详情
      const houseIds = favoriteResult.data.map(item => item.house_id);
      const houseResult = await db.collection('house')
        .where({
          _id: dbCmd.in(houseIds),
          status: 'approved'
        })
        .get();

      // 合并数据
      const list = favoriteResult.data.map(favorite => {
        const house = houseResult.data.find(h => h._id === favorite.house_id);
        return {
          ...favorite,
          house_info: house
        };
      }).filter(item => item.house_info); // 过滤掉已删除的房源

      // 获取总数
      const countResult = await db.collection('user_favorites').where({
        user_id: userInfo.uid
      }).count();

      return {
        code: 0,
        message: '获取成功',
        data: {
          list,
          total: countResult.total,
          page,
          limit,
          totalPages: Math.ceil(countResult.total / limit)
        }
      };
    } catch (error) {
      console.error('Get favorite list error:', error);
      return {
        code: 500,
        message: '获取收藏列表失败',
        error: error.message
      };
    }
  }
}

module.exports = new HouseController();
