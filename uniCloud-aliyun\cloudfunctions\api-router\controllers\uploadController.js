const db = uniCloud.database();

class UploadController {
  // 上传图片
  async uploadImage(event, context) {
    try {
      const { userInfo, file, fileName, fileType } = event;
      
      if (!file) {
        return {
          code: 400,
          message: '文件不能为空'
        };
      }
      
      // 验证文件类型
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (fileType && !allowedTypes.includes(fileType.toLowerCase())) {
        return {
          code: 400,
          message: '不支持的文件类型，仅支持 JPEG、PNG、GIF、WebP 格式'
        };
      }
      
      // 生成文件名
      const timestamp = Date.now();
      const randomStr = Math.random().toString(36).substring(2);
      const extension = this.getFileExtension(fileName || 'image.jpg');
      const cloudFileName = `house-images/${userInfo.uid}/${timestamp}_${randomStr}.${extension}`;
      
      try {
        // 上传到云存储
        const uploadResult = await uniCloud.uploadFile({
          cloudPath: cloudFileName,
          fileContent: Buffer.from(file, 'base64')
        });
        
        if (!uploadResult.fileID) {
          return {
            code: 500,
            message: '文件上传失败'
          };
        }
        
        // 获取临时访问链接
        const getTempFileURLResult = await uniCloud.getTempFileURL({
          fileList: [uploadResult.fileID]
        });
        
        let fileURL = uploadResult.fileID;
        if (getTempFileURLResult.fileList && getTempFileURLResult.fileList.length > 0) {
          fileURL = getTempFileURLResult.fileList[0].tempFileURL || uploadResult.fileID;
        }
        
        // 记录上传信息到数据库（可选）
        await db.collection('upload_files').add({
          user_id: userInfo.uid,
          file_id: uploadResult.fileID,
          file_name: fileName || 'image.jpg',
          file_type: fileType || 'image/jpeg',
          file_size: Buffer.from(file, 'base64').length,
          cloud_path: cloudFileName,
          file_url: fileURL,
          created_at: new Date()
        });
        
        return {
          code: 0,
          message: '上传成功',
          data: {
            fileID: uploadResult.fileID,
            fileURL: fileURL,
            cloudPath: cloudFileName
          }
        };
      } catch (uploadError) {
        console.error('Upload to cloud storage error:', uploadError);
        return {
          code: 500,
          message: '云存储上传失败',
          error: uploadError.message
        };
      }
    } catch (error) {
      console.error('Upload image error:', error);
      return {
        code: 500,
        message: '上传图片失败',
        error: error.message
      };
    }
  }

  // 批量上传图片
  async uploadImages(event, context) {
    try {
      const { userInfo, files } = event;
      
      if (!files || !Array.isArray(files) || files.length === 0) {
        return {
          code: 400,
          message: '文件列表不能为空'
        };
      }
      
      if (files.length > 10) {
        return {
          code: 400,
          message: '单次最多上传10张图片'
        };
      }
      
      const uploadResults = [];
      const errors = [];
      
      for (let i = 0; i < files.length; i++) {
        const fileData = files[i];
        try {
          const result = await this.uploadImage({
            userInfo,
            file: fileData.file,
            fileName: fileData.fileName,
            fileType: fileData.fileType
          }, context);
          
          if (result.code === 0) {
            uploadResults.push(result.data);
          } else {
            errors.push({
              index: i,
              fileName: fileData.fileName,
              error: result.message
            });
          }
        } catch (error) {
          errors.push({
            index: i,
            fileName: fileData.fileName,
            error: error.message
          });
        }
      }
      
      return {
        code: 0,
        message: `成功上传 ${uploadResults.length} 张图片`,
        data: {
          success: uploadResults,
          errors: errors,
          successCount: uploadResults.length,
          errorCount: errors.length
        }
      };
    } catch (error) {
      console.error('Upload images error:', error);
      return {
        code: 500,
        message: '批量上传失败',
        error: error.message
      };
    }
  }

  // 删除文件
  async deleteFile(event, context) {
    try {
      const { userInfo, fileID } = event;
      
      if (!fileID) {
        return {
          code: 400,
          message: '文件ID不能为空'
        };
      }
      
      // 检查文件是否属于当前用户或用户是否为管理员
      const fileDoc = await db.collection('upload_files').where({
        file_id: fileID
      }).get();
      
      if (fileDoc.data.length > 0) {
        const file = fileDoc.data[0];
        if (file.user_id !== userInfo.uid && !userInfo.role.includes('admin')) {
          return {
            code: 403,
            message: '无权删除此文件'
          };
        }
      }
      
      try {
        // 从云存储删除文件
        await uniCloud.deleteFile({
          fileList: [fileID]
        });
        
        // 从数据库删除记录
        await db.collection('upload_files').where({
          file_id: fileID
        }).remove();
        
        return {
          code: 0,
          message: '文件删除成功'
        };
      } catch (deleteError) {
        console.error('Delete from cloud storage error:', deleteError);
        return {
          code: 500,
          message: '删除文件失败',
          error: deleteError.message
        };
      }
    } catch (error) {
      console.error('Delete file error:', error);
      return {
        code: 500,
        message: '删除文件失败',
        error: error.message
      };
    }
  }

  // 获取用户上传的文件列表
  async getUserFiles(event, context) {
    try {
      const { userInfo, page = 1, limit = 20, fileType } = event;
      
      const skip = (page - 1) * limit;
      
      let whereCondition = {
        user_id: userInfo.uid
      };
      
      if (fileType) {
        whereCondition.file_type = new RegExp(fileType, 'i');
      }
      
      const result = await db.collection('upload_files')
        .where(whereCondition)
        .orderBy('created_at', 'desc')
        .skip(skip)
        .limit(limit)
        .get();
      
      const countResult = await db.collection('upload_files').where(whereCondition).count();
      
      return {
        code: 0,
        message: '获取成功',
        data: {
          list: result.data,
          total: countResult.total,
          page,
          limit,
          totalPages: Math.ceil(countResult.total / limit)
        }
      };
    } catch (error) {
      console.error('Get user files error:', error);
      return {
        code: 500,
        message: '获取文件列表失败',
        error: error.message
      };
    }
  }

  // 获取临时访问链接
  async getTempFileURL(event, context) {
    try {
      const { fileID, fileList } = event;
      
      let fileIds = [];
      if (fileID) {
        fileIds = [fileID];
      } else if (fileList && Array.isArray(fileList)) {
        fileIds = fileList;
      } else {
        return {
          code: 400,
          message: '请提供文件ID或文件ID列表'
        };
      }
      
      const result = await uniCloud.getTempFileURL({
        fileList: fileIds
      });
      
      return {
        code: 0,
        message: '获取成功',
        data: result.fileList
      };
    } catch (error) {
      console.error('Get temp file URL error:', error);
      return {
        code: 500,
        message: '获取临时链接失败',
        error: error.message
      };
    }
  }

  // 工具方法：获取文件扩展名
  getFileExtension(fileName) {
    const lastDotIndex = fileName.lastIndexOf('.');
    if (lastDotIndex === -1) {
      return 'jpg'; // 默认扩展名
    }
    return fileName.substring(lastDotIndex + 1).toLowerCase();
  }

  // 工具方法：验证图片格式
  isValidImageType(fileType) {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    return allowedTypes.includes(fileType.toLowerCase());
  }

  // 工具方法：获取文件大小（字节）
  getFileSize(base64String) {
    return Buffer.from(base64String, 'base64').length;
  }
}

module.exports = new UploadController();
