const uniID = require('uni-id-common');
const db = uniCloud.database();

class UserController {
  // 用户注册
  async register(event, context) {
    try {
      const { username, password, nickname, phone, email } = event;
      
      // 参数验证
      if (!username || !password) {
        return {
          code: 400,
          message: '用户名和密码不能为空'
        };
      }
      
      if (username.length < 3 || username.length > 50) {
        return {
          code: 400,
          message: '用户名长度必须在3-50个字符之间'
        };
      }
      
      if (password.length < 6) {
        return {
          code: 400,
          message: '密码长度不能少于6位'
        };
      }

      // 检查用户名是否已存在
      const existUser = await db.collection('user').where({
        username
      }).get();
      
      if (existUser.data.length > 0) {
        return {
          code: 400,
          message: '用户名已存在'
        };
      }

      // 如果提供了手机号，检查是否已存在
      if (phone) {
        const existPhone = await db.collection('user').where({
          phone
        }).get();
        
        if (existPhone.data.length > 0) {
          return {
            code: 400,
            message: '手机号已被注册'
          };
        }
      }

      // 如果提供了邮箱，检查是否已存在
      if (email) {
        const existEmail = await db.collection('user').where({
          email
        }).get();
        
        if (existEmail.data.length > 0) {
          return {
            code: 400,
            message: '邮箱已被注册'
          };
        }
      }

      // 使用 uni-id 注册用户
      const registerResult = await uniID.register({
        username,
        password,
        nickname: nickname || username,
        mobile: phone,
        email,
        role: ['user']
      });

      if (registerResult.code !== 0) {
        return {
          code: 400,
          message: registerResult.msg || '注册失败'
        };
      }

      return {
        code: 0,
        message: '注册成功',
        data: {
          uid: registerResult.uid,
          username,
          nickname: nickname || username
        }
      };
    } catch (error) {
      console.error('Register error:', error);
      return {
        code: 500,
        message: '注册失败',
        error: error.message
      };
    }
  }

  // 用户登录
  async login(event, context) {
    try {
      const { username, password } = event;
      
      if (!username || !password) {
        return {
          code: 400,
          message: '用户名和密码不能为空'
        };
      }

      // 使用 uni-id 登录
      const loginResult = await uniID.login({
        username,
        password
      });

      if (loginResult.code !== 0) {
        return {
          code: 400,
          message: loginResult.msg || '登录失败'
        };
      }

      // 更新最后登录时间
      await db.collection('user').doc(loginResult.uid).update({
        last_login_at: new Date()
      });

      return {
        code: 0,
        message: '登录成功',
        data: {
          token: loginResult.token,
          uid: loginResult.uid,
          username: loginResult.userInfo.username,
          nickname: loginResult.userInfo.nickname,
          avatar: loginResult.userInfo.avatar,
          role: loginResult.userInfo.role
        }
      };
    } catch (error) {
      console.error('Login error:', error);
      return {
        code: 500,
        message: '登录失败',
        error: error.message
      };
    }
  }

  // 用户登出
  async logout(event, context) {
    try {
      const { token } = event;
      
      const logoutResult = await uniID.logout(token);
      
      return {
        code: 0,
        message: '登出成功'
      };
    } catch (error) {
      console.error('Logout error:', error);
      return {
        code: 500,
        message: '登出失败',
        error: error.message
      };
    }
  }

  // 获取用户信息
  async getProfile(event, context) {
    try {
      const { userInfo } = event;
      
      const userDoc = await db.collection('user').doc(userInfo.uid).get();
      
      if (!userDoc.data.length) {
        return {
          code: 404,
          message: '用户不存在'
        };
      }

      const user = userDoc.data[0];
      
      // 移除敏感信息
      delete user.password;
      delete user.token;
      
      return {
        code: 0,
        message: '获取成功',
        data: user
      };
    } catch (error) {
      console.error('Get profile error:', error);
      return {
        code: 500,
        message: '获取用户信息失败',
        error: error.message
      };
    }
  }

  // 更新用户信息
  async updateProfile(event, context) {
    try {
      const { userInfo, nickname, avatar, phone, email } = event;
      
      const updateData = {
        updated_at: new Date()
      };
      
      if (nickname !== undefined) {
        if (nickname.length > 50) {
          return {
            code: 400,
            message: '昵称长度不能超过50个字符'
          };
        }
        updateData.nickname = nickname;
      }
      
      if (avatar !== undefined) {
        updateData.avatar = avatar;
      }
      
      if (phone !== undefined) {
        // 检查手机号是否已被其他用户使用
        const existPhone = await db.collection('user').where({
          phone,
          _id: db.command.neq(userInfo.uid)
        }).get();
        
        if (existPhone.data.length > 0) {
          return {
            code: 400,
            message: '手机号已被其他用户使用'
          };
        }
        updateData.phone = phone;
      }
      
      if (email !== undefined) {
        // 检查邮箱是否已被其他用户使用
        const existEmail = await db.collection('user').where({
          email,
          _id: db.command.neq(userInfo.uid)
        }).get();
        
        if (existEmail.data.length > 0) {
          return {
            code: 400,
            message: '邮箱已被其他用户使用'
          };
        }
        updateData.email = email;
      }

      await db.collection('user').doc(userInfo.uid).update(updateData);
      
      return {
        code: 0,
        message: '更新成功'
      };
    } catch (error) {
      console.error('Update profile error:', error);
      return {
        code: 500,
        message: '更新用户信息失败',
        error: error.message
      };
    }
  }

  // 修改密码
  async changePassword(event, context) {
    try {
      const { userInfo, oldPassword, newPassword } = event;
      
      if (!oldPassword || !newPassword) {
        return {
          code: 400,
          message: '旧密码和新密码不能为空'
        };
      }
      
      if (newPassword.length < 6) {
        return {
          code: 400,
          message: '新密码长度不能少于6位'
        };
      }

      // 使用 uni-id 修改密码
      const result = await uniID.updatePwd({
        uid: userInfo.uid,
        oldPassword,
        newPassword
      });

      if (result.code !== 0) {
        return {
          code: 400,
          message: result.msg || '修改密码失败'
        };
      }

      return {
        code: 0,
        message: '密码修改成功'
      };
    } catch (error) {
      console.error('Change password error:', error);
      return {
        code: 500,
        message: '修改密码失败',
        error: error.message
      };
    }
  }

  // 重置密码（通过手机号或邮箱）
  async resetPassword(event, context) {
    try {
      const { username, phone, email, code, newPassword } = event;
      
      // 这里简化处理，实际应该有验证码验证流程
      if (!newPassword) {
        return {
          code: 400,
          message: '新密码不能为空'
        };
      }
      
      if (newPassword.length < 6) {
        return {
          code: 400,
          message: '新密码长度不能少于6位'
        };
      }

      let whereCondition = {};
      if (username) {
        whereCondition.username = username;
      } else if (phone) {
        whereCondition.phone = phone;
      } else if (email) {
        whereCondition.email = email;
      } else {
        return {
          code: 400,
          message: '请提供用户名、手机号或邮箱'
        };
      }

      // 查找用户
      const userDoc = await db.collection('user').where(whereCondition).get();
      
      if (!userDoc.data.length) {
        return {
          code: 404,
          message: '用户不存在'
        };
      }

      const user = userDoc.data[0];

      // 使用 uni-id 重置密码
      const result = await uniID.resetPwd({
        uid: user._id,
        password: newPassword
      });

      if (result.code !== 0) {
        return {
          code: 400,
          message: result.msg || '重置密码失败'
        };
      }

      return {
        code: 0,
        message: '密码重置成功'
      };
    } catch (error) {
      console.error('Reset password error:', error);
      return {
        code: 500,
        message: '重置密码失败',
        error: error.message
      };
    }
  }
}

module.exports = new UserController();
