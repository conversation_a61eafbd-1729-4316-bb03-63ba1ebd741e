'use strict';

const uniID = require('uni-id-common');
const { Router } = require('./router');
const userController = require('./controllers/userController');
const houseController = require('./controllers/houseController');
const adminController = require('./controllers/adminController');
const uploadController = require('./controllers/uploadController');
const SecurityMiddleware = require('./middleware/security');

// 创建路由实例
const router = new Router();

// 全局安全中间件
const globalSecurityMiddleware = [
  SecurityMiddleware.inputValidationMiddleware,
  SecurityMiddleware.sqlInjectionMiddleware,
  SecurityMiddleware.rateLimitMiddleware
];

// 中间件：身份验证
const authMiddleware = async (event, context) => {
  const { token } = event;
  if (!token) {
    return {
      code: 401,
      message: '未提供认证令牌'
    };
  }

  try {
    const payload = await uniID.checkToken(token);
    if (payload.code !== 0) {
      return {
        code: 401,
        message: '认证令牌无效'
      };
    }

    // 将用户信息添加到 event 中
    event.userInfo = payload;

    // 检查用户状态
    const userStatusResult = await SecurityMiddleware.userStatusMiddleware(event, context);
    if (userStatusResult) {
      return userStatusResult;
    }

    return null; // 继续执行
  } catch (error) {
    return {
      code: 401,
      message: '认证失败'
    };
  }
};

// 中间件：管理员权限验证
const adminMiddleware = async (event, context) => {
  const { userInfo } = event;
  if (!userInfo || !userInfo.role || !userInfo.role.includes('admin')) {
    return {
      code: 403,
      message: '权限不足'
    };
  }
  return null; // 继续执行
};

// 用户相关路由（无需认证）
router.post('/user/register', [...globalSecurityMiddleware], userController.register);
router.post('/user/login', [...globalSecurityMiddleware], userController.login);
router.post('/user/reset-password', [...globalSecurityMiddleware], userController.resetPassword);

// 用户相关路由（需要认证）
router.post('/user/logout', [...globalSecurityMiddleware, authMiddleware], userController.logout);
router.get('/user/profile', [...globalSecurityMiddleware, authMiddleware], userController.getProfile);
router.put('/user/profile', [...globalSecurityMiddleware, authMiddleware], userController.updateProfile);
router.put('/user/password', [...globalSecurityMiddleware, authMiddleware], userController.changePassword);

// 房源相关路由（公开）
router.get('/house/list', houseController.getHouseList);
router.get('/house/detail/:id', houseController.getHouseDetail);
router.get('/house/search', houseController.searchHouses);

// 房源相关路由（需要认证）
router.post('/house/create', [authMiddleware], houseController.createHouse);
router.put('/house/update/:id', [authMiddleware], houseController.updateHouse);
router.delete('/house/delete/:id', [authMiddleware], houseController.deleteHouse);
router.get('/house/my-houses', [authMiddleware], houseController.getMyHouses);

// 收藏相关路由（需要认证）
router.post('/favorite/add', [authMiddleware], houseController.addFavorite);
router.delete('/favorite/remove', [authMiddleware], houseController.removeFavorite);
router.get('/favorite/list', [authMiddleware], houseController.getFavoriteList);

// 文件上传路由（需要认证）
router.post('/upload/image', [...globalSecurityMiddleware, authMiddleware, SecurityMiddleware.fileUploadSecurityMiddleware], uploadController.uploadImage);

// 管理员路由（需要管理员权限）
router.get('/admin/users', [authMiddleware, adminMiddleware], adminController.getUserList);
router.put('/admin/user/ban/:id', [authMiddleware, adminMiddleware], adminController.banUser);
router.put('/admin/user/unban/:id', [authMiddleware, adminMiddleware], adminController.unbanUser);
router.get('/admin/houses', [authMiddleware, adminMiddleware], adminController.getHouseList);
router.put('/admin/house/approve/:id', [authMiddleware, adminMiddleware], adminController.approveHouse);
router.put('/admin/house/reject/:id', [authMiddleware, adminMiddleware], adminController.rejectHouse);
router.get('/admin/statistics', [authMiddleware, adminMiddleware], adminController.getStatistics);
router.get('/admin/config', [authMiddleware, adminMiddleware], adminController.getSystemConfig);
router.put('/admin/config', [authMiddleware, adminMiddleware], adminController.updateSystemConfig);

// 云函数入口
exports.main = async (event, context) => {
  try {
    // 解析请求
    const { httpMethod = 'GET', path = '/', body, queryStringParameters = {} } = event;
    
    // 合并查询参数和请求体参数
    const params = {
      ...queryStringParameters,
      ...(body ? (typeof body === 'string' ? JSON.parse(body) : body) : {})
    };
    
    // 将参数添加到 event 中
    Object.assign(event, params);
    
    // 路由处理
    const result = await router.handle(httpMethod, path, event, context);
    
    return {
      statusCode: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type,Authorization,token'
      },
      body: JSON.stringify(result)
    };
  } catch (error) {
    console.error('API Router Error:', error);
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify({
        code: 500,
        message: '服务器内部错误',
        error: error.message
      })
    };
  }
};
