const db = uniCloud.database();
const dbCmd = db.command;

class SecurityMiddleware {
  // 防刷中间件
  static async rateLimitMiddleware(event, context) {
    try {
      const { userInfo, clientIP } = event;
      const identifier = userInfo ? userInfo.uid : (clientIP || 'unknown');
      const now = new Date();
      const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);
      
      // 检查一分钟内的请求次数
      const requestCount = await db.collection('api_requests').where({
        identifier,
        created_at: dbCmd.gte(oneMinuteAgo)
      }).count();
      
      // 限制每分钟最多60次请求
      if (requestCount.total >= 60) {
        return {
          code: 429,
          message: '请求过于频繁，请稍后再试'
        };
      }
      
      // 记录请求
      await db.collection('api_requests').add({
        identifier,
        ip: clientIP || 'unknown',
        user_id: userInfo ? userInfo.uid : null,
        created_at: now
      });
      
      // 清理过期记录（保留最近1小时的记录）
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      await db.collection('api_requests').where({
        created_at: dbCmd.lt(oneHourAgo)
      }).remove();
      
      return null; // 继续执行
    } catch (error) {
      console.error('Rate limit middleware error:', error);
      return null; // 出错时不阻止请求
    }
  }

  // 输入验证中间件
  static async inputValidationMiddleware(event, context) {
    try {
      // XSS 防护
      const dangerousPatterns = [
        /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
        /javascript:/gi,
        /on\w+\s*=/gi,
        /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi
      ];
      
      const checkXSS = (value) => {
        if (typeof value === 'string') {
          for (const pattern of dangerousPatterns) {
            if (pattern.test(value)) {
              return true;
            }
          }
        }
        return false;
      };
      
      const validateObject = (obj) => {
        for (const key in obj) {
          if (obj.hasOwnProperty(key)) {
            const value = obj[key];
            if (typeof value === 'string' && checkXSS(value)) {
              return {
                code: 400,
                message: `参数 ${key} 包含不安全内容`
              };
            } else if (typeof value === 'object' && value !== null) {
              const result = validateObject(value);
              if (result) return result;
            }
          }
        }
        return null;
      };
      
      const validationResult = validateObject(event);
      if (validationResult) {
        return validationResult;
      }
      
      return null; // 继续执行
    } catch (error) {
      console.error('Input validation middleware error:', error);
      return null; // 出错时不阻止请求
    }
  }

  // SQL注入防护中间件
  static async sqlInjectionMiddleware(event, context) {
    try {
      const sqlPatterns = [
        /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,
        /(\b(OR|AND)\s+\d+\s*=\s*\d+)/gi,
        /(\'|\"|;|--|\*|\/\*|\*\/)/gi
      ];
      
      const checkSQLInjection = (value) => {
        if (typeof value === 'string') {
          for (const pattern of sqlPatterns) {
            if (pattern.test(value)) {
              return true;
            }
          }
        }
        return false;
      };
      
      const validateObject = (obj) => {
        for (const key in obj) {
          if (obj.hasOwnProperty(key)) {
            const value = obj[key];
            if (typeof value === 'string' && checkSQLInjection(value)) {
              return {
                code: 400,
                message: `参数 ${key} 包含不安全的SQL字符`
              };
            } else if (typeof value === 'object' && value !== null) {
              const result = validateObject(value);
              if (result) return result;
            }
          }
        }
        return null;
      };
      
      const validationResult = validateObject(event);
      if (validationResult) {
        return validationResult;
      }
      
      return null; // 继续执行
    } catch (error) {
      console.error('SQL injection middleware error:', error);
      return null; // 出错时不阻止请求
    }
  }

  // 用户状态检查中间件
  static async userStatusMiddleware(event, context) {
    try {
      const { userInfo } = event;
      
      if (!userInfo || !userInfo.uid) {
        return null; // 未登录用户跳过检查
      }
      
      // 检查用户是否被封禁
      const userDoc = await db.collection('user').doc(userInfo.uid).get();
      
      if (!userDoc.data.length) {
        return {
          code: 401,
          message: '用户不存在'
        };
      }
      
      const user = userDoc.data[0];
      
      if (user.is_banned) {
        return {
          code: 403,
          message: '账号已被封禁，请联系管理员'
        };
      }
      
      return null; // 继续执行
    } catch (error) {
      console.error('User status middleware error:', error);
      return null; // 出错时不阻止请求
    }
  }

  // 文件上传安全检查中间件
  static async fileUploadSecurityMiddleware(event, context) {
    try {
      const { file, fileName, fileType } = event;
      
      if (!file) {
        return null; // 没有文件上传跳过检查
      }
      
      // 检查文件类型
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (fileType && !allowedTypes.includes(fileType.toLowerCase())) {
        return {
          code: 400,
          message: '不支持的文件类型'
        };
      }
      
      // 检查文件大小（限制为5MB）
      const maxSize = 5 * 1024 * 1024; // 5MB
      const fileSize = Buffer.from(file, 'base64').length;
      if (fileSize > maxSize) {
        return {
          code: 400,
          message: '文件大小不能超过5MB'
        };
      }
      
      // 检查文件名
      if (fileName) {
        const dangerousExtensions = ['.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js'];
        const fileExtension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
        if (dangerousExtensions.includes(fileExtension)) {
          return {
            code: 400,
            message: '不允许上传可执行文件'
          };
        }
      }
      
      return null; // 继续执行
    } catch (error) {
      console.error('File upload security middleware error:', error);
      return null; // 出错时不阻止请求
    }
  }

  // 数据脱敏工具
  static sanitizeUserData(userData) {
    if (!userData) return userData;
    
    const sanitized = { ...userData };
    
    // 移除敏感字段
    delete sanitized.password;
    delete sanitized.token;
    
    // 脱敏手机号
    if (sanitized.phone) {
      sanitized.phone = sanitized.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    }
    
    // 脱敏邮箱
    if (sanitized.email) {
      const [username, domain] = sanitized.email.split('@');
      if (username.length > 2) {
        const maskedUsername = username.substring(0, 2) + '*'.repeat(username.length - 2);
        sanitized.email = maskedUsername + '@' + domain;
      }
    }
    
    return sanitized;
  }

  // 生成安全的随机字符串
  static generateSecureRandomString(length = 32) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  // 验证密码强度
  static validatePasswordStrength(password) {
    if (!password || password.length < 6) {
      return {
        valid: false,
        message: '密码长度不能少于6位'
      };
    }
    
    if (password.length > 128) {
      return {
        valid: false,
        message: '密码长度不能超过128位'
      };
    }
    
    // 检查是否包含常见弱密码
    const weakPasswords = ['123456', 'password', '123456789', '12345678', 'qwerty', 'abc123'];
    if (weakPasswords.includes(password.toLowerCase())) {
      return {
        valid: false,
        message: '密码过于简单，请使用更复杂的密码'
      };
    }
    
    return {
      valid: true,
      message: '密码强度合格'
    };
  }
}

module.exports = SecurityMiddleware;
