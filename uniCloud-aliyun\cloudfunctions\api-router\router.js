class Router {
  constructor() {
    this.routes = [];
  }

  // 添加路由
  addRoute(method, path, middlewares, handler) {
    // 如果 middlewares 是函数，说明没有中间件，直接是处理函数
    if (typeof middlewares === 'function') {
      handler = middlewares;
      middlewares = [];
    }
    
    // 确保 middlewares 是数组
    if (!Array.isArray(middlewares)) {
      middlewares = [middlewares];
    }

    // 将路径转换为正则表达式
    const paramNames = [];
    const regexPath = path
      .replace(/:([^/]+)/g, (match, paramName) => {
        paramNames.push(paramName);
        return '([^/]+)';
      })
      .replace(/\*/g, '.*');

    this.routes.push({
      method: method.toUpperCase(),
      path,
      regex: new RegExp(`^${regexPath}$`),
      paramNames,
      middlewares,
      handler
    });
  }

  // HTTP 方法快捷方式
  get(path, middlewares, handler) {
    this.addRoute('GET', path, middlewares, handler);
  }

  post(path, middlewares, handler) {
    this.addRoute('POST', path, middlewares, handler);
  }

  put(path, middlewares, handler) {
    this.addRoute('PUT', path, middlewares, handler);
  }

  delete(path, middlewares, handler) {
    this.addRoute('DELETE', path, middlewares, handler);
  }

  patch(path, middlewares, handler) {
    this.addRoute('PATCH', path, middlewares, handler);
  }

  // 处理请求
  async handle(method, path, event, context) {
    const route = this.findRoute(method, path);
    
    if (!route) {
      return {
        code: 404,
        message: '接口不存在'
      };
    }

    // 提取路径参数
    const match = path.match(route.regex);
    if (match) {
      const params = {};
      route.paramNames.forEach((name, index) => {
        params[name] = match[index + 1];
      });
      event.params = params;
    }

    try {
      // 执行中间件
      for (const middleware of route.middlewares) {
        const result = await middleware(event, context);
        if (result) {
          // 中间件返回结果，说明需要中断执行
          return result;
        }
      }

      // 执行处理函数
      const result = await route.handler(event, context);
      return result;
    } catch (error) {
      console.error('Route handler error:', error);
      return {
        code: 500,
        message: '处理请求时发生错误',
        error: error.message
      };
    }
  }

  // 查找匹配的路由
  findRoute(method, path) {
    return this.routes.find(route => 
      route.method === method.toUpperCase() && 
      route.regex.test(path)
    );
  }
}

module.exports = { Router };
