// API 测试用例
// 使用方法：在 HBuilderX 中运行此文件进行 API 测试

const testConfig = {
  // 测试用户信息
  testUser: {
    username: 'testuser001',
    password: '123456',
    nickname: '测试用户',
    phone: '13800138000',
    email: '<EMAIL>'
  },
  // 测试房源信息
  testHouse: {
    title: '测试房源标题',
    desc: '这是一个测试房源的详细描述',
    location: {
      address: '北京市朝阳区测试街道123号',
      latitude: 39.9042,
      longitude: 116.4074,
      district: '朝阳区',
      subway: '国贸站'
    },
    price: 3000,
    type: '一居',
    config: ['空调', '洗衣机', '冰箱', 'WiFi'],
    contact: {
      phone: '13800138000',
      wechat: 'testwechat'
    },
    area: 50,
    floor: '10/20',
    orientation: '南',
    is_furnished: true
  }
};

// 测试结果存储
let testResults = {
  passed: 0,
  failed: 0,
  errors: []
};

// 测试工具函数
function assert(condition, message) {
  if (condition) {
    testResults.passed++;
    console.log(`✅ PASS: ${message}`);
  } else {
    testResults.failed++;
    testResults.errors.push(message);
    console.log(`❌ FAIL: ${message}`);
  }
}

function assertEqual(actual, expected, message) {
  assert(actual === expected, `${message} - Expected: ${expected}, Actual: ${actual}`);
}

function assertNotNull(value, message) {
  assert(value !== null && value !== undefined, `${message} - Value should not be null`);
}

// 模拟云函数调用
async function callAPI(path, method = 'POST', data = {}, token = null) {
  try {
    const event = {
      httpMethod: method,
      path: path,
      body: JSON.stringify(data),
      token: token
    };
    
    // 这里应该调用实际的云函数
    // const result = await uniCloud.callFunction({
    //   name: 'api-router',
    //   data: event
    // });
    
    // 模拟返回结果
    console.log(`📡 API Call: ${method} ${path}`);
    console.log(`📤 Request:`, data);
    
    return {
      code: 0,
      message: 'Success',
      data: {}
    };
  } catch (error) {
    console.error(`API调用失败: ${path}`, error);
    return {
      code: 500,
      message: error.message
    };
  }
}

// 测试用例
async function runTests() {
  console.log('🚀 开始 API 测试...\n');
  
  let userToken = null;
  let houseId = null;
  
  // 1. 用户注册测试
  console.log('📝 测试用户注册...');
  try {
    const registerResult = await callAPI('/user/register', 'POST', testConfig.testUser);
    assert(registerResult.code === 0, '用户注册成功');
    assertNotNull(registerResult.data, '注册返回数据不为空');
  } catch (error) {
    testResults.errors.push(`用户注册测试失败: ${error.message}`);
  }
  
  // 2. 用户登录测试
  console.log('\n🔐 测试用户登录...');
  try {
    const loginResult = await callAPI('/user/login', 'POST', {
      username: testConfig.testUser.username,
      password: testConfig.testUser.password
    });
    assert(loginResult.code === 0, '用户登录成功');
    assertNotNull(loginResult.data?.token, '登录返回token');
    userToken = loginResult.data?.token;
  } catch (error) {
    testResults.errors.push(`用户登录测试失败: ${error.message}`);
  }
  
  // 3. 获取用户信息测试
  console.log('\n👤 测试获取用户信息...');
  try {
    const profileResult = await callAPI('/user/profile', 'GET', {}, userToken);
    assert(profileResult.code === 0, '获取用户信息成功');
    assertNotNull(profileResult.data, '用户信息不为空');
  } catch (error) {
    testResults.errors.push(`获取用户信息测试失败: ${error.message}`);
  }
  
  // 4. 发布房源测试
  console.log('\n🏠 测试发布房源...');
  try {
    const createHouseResult = await callAPI('/house/create', 'POST', testConfig.testHouse, userToken);
    assert(createHouseResult.code === 0, '发布房源成功');
    assertNotNull(createHouseResult.data?.id, '返回房源ID');
    houseId = createHouseResult.data?.id;
  } catch (error) {
    testResults.errors.push(`发布房源测试失败: ${error.message}`);
  }
  
  // 5. 获取房源列表测试
  console.log('\n📋 测试获取房源列表...');
  try {
    const houseListResult = await callAPI('/house/list', 'GET', {
      page: 1,
      limit: 10,
      status: 'approved'
    });
    assert(houseListResult.code === 0, '获取房源列表成功');
    assertNotNull(houseListResult.data?.list, '房源列表不为空');
  } catch (error) {
    testResults.errors.push(`获取房源列表测试失败: ${error.message}`);
  }
  
  // 6. 搜索房源测试
  console.log('\n🔍 测试搜索房源...');
  try {
    const searchResult = await callAPI('/house/search', 'GET', {
      keyword: '测试',
      page: 1,
      limit: 10
    });
    assert(searchResult.code === 0, '搜索房源成功');
    assertNotNull(searchResult.data, '搜索结果不为空');
  } catch (error) {
    testResults.errors.push(`搜索房源测试失败: ${error.message}`);
  }
  
  // 7. 添加收藏测试
  if (houseId) {
    console.log('\n❤️ 测试添加收藏...');
    try {
      const favoriteResult = await callAPI('/favorite/add', 'POST', {
        house_id: houseId
      }, userToken);
      assert(favoriteResult.code === 0, '添加收藏成功');
    } catch (error) {
      testResults.errors.push(`添加收藏测试失败: ${error.message}`);
    }
  }
  
  // 8. 获取收藏列表测试
  console.log('\n📚 测试获取收藏列表...');
  try {
    const favoriteListResult = await callAPI('/favorite/list', 'GET', {
      page: 1,
      limit: 10
    }, userToken);
    assert(favoriteListResult.code === 0, '获取收藏列表成功');
    assertNotNull(favoriteListResult.data, '收藏列表数据不为空');
  } catch (error) {
    testResults.errors.push(`获取收藏列表测试失败: ${error.message}`);
  }
  
  // 9. 文件上传测试（模拟）
  console.log('\n📤 测试文件上传...');
  try {
    const uploadResult = await callAPI('/upload/image', 'POST', {
      file: 'base64_encoded_image_data',
      fileName: 'test.jpg',
      fileType: 'image/jpeg'
    }, userToken);
    assert(uploadResult.code === 0, '文件上传成功');
  } catch (error) {
    testResults.errors.push(`文件上传测试失败: ${error.message}`);
  }
  
  // 10. 用户登出测试
  console.log('\n🚪 测试用户登出...');
  try {
    const logoutResult = await callAPI('/user/logout', 'POST', {}, userToken);
    assert(logoutResult.code === 0, '用户登出成功');
  } catch (error) {
    testResults.errors.push(`用户登出测试失败: ${error.message}`);
  }
  
  // 输出测试结果
  console.log('\n📊 测试结果统计:');
  console.log(`✅ 通过: ${testResults.passed}`);
  console.log(`❌ 失败: ${testResults.failed}`);
  console.log(`📈 成功率: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(2)}%`);
  
  if (testResults.errors.length > 0) {
    console.log('\n❌ 失败详情:');
    testResults.errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error}`);
    });
  }
  
  console.log('\n🎉 API 测试完成!');
}

// 运行测试
runTests().catch(console.error);
