'use strict';

/**
 * 数据库初始化云函数
 * 用于创建数据库集合和初始化测试数据
 */

exports.main = async (event, context) => {
  const db = uniCloud.database();

  try {
    console.log('开始初始化数据库...');

    // 确保数据库集合存在 - 通过尝试访问来创建集合
    console.log('检查并创建数据库集合...');

    // 先尝试创建集合（如果不存在会自动创建）
    const userCollection = db.collection('user');
    const houseCollection = db.collection('house');

    // 测试集合是否可访问
    try {
      await userCollection.limit(1).get();
      console.log('user 集合访问正常');
    } catch (error) {
      console.log('user 集合不存在，将通过插入数据创建');
    }

    try {
      await houseCollection.limit(1).get();
      console.log('house 集合访问正常');
    } catch (error) {
      console.log('house 集合不存在，将通过插入数据创建');
    }

    // 创建测试用户
    console.log('创建测试用户...');
    
    const testUser = {
      username: 'testuser',
      password: 'e10adc3949ba59abbe56e057f20f883e', // 123456 的 MD5
      nickname: '测试用户',
      avatar: '',
      phone: '13800138000',
      is_banned: false,
      created_at: new Date(),
      favorites: []
    };
    
    // 检查测试用户是否已存在
    const existUser = await userCollection.where({ username: 'testuser' }).get();
    if (existUser.data.length === 0) {
      const userResult = await userCollection.add(testUser);
      console.log('测试用户创建成功，ID:', userResult.id);
    } else {
      console.log('测试用户已存在');
    }
    
    // 创建测试房源
    console.log('创建测试房源...');
    const houseCollection = db.collection('house');
    
    const testHouses = [
      {
        title: '精装一居室，拎包入住',
        desc: '房间干净整洁，家具家电齐全，交通便利，适合上班族居住。周边配套设施完善，生活便利。',
        images: [
          'https://via.placeholder.com/750x500/4CAF50/FFFFFF?text=精装一居室'
        ],
        location: {
          latitude: 39.908823,
          longitude: 116.397470,
          address: '北京市朝阳区建国门外大街1号'
        },
        price: 3500,
        type: '一居室',
        config: ['空调', '洗衣机', '冰箱', '热水器', '宽带'],
        contact: {
          phone: '13800138001',
          wechat: 'wechat123'
        },
        owner_id: 'test_user_id',
        status: 'approved',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        title: '温馨二居室，南北通透',
        desc: '房屋采光良好，南北通透，装修温馨，适合小家庭居住。小区环境优美，物业管理完善。',
        images: [
          'https://via.placeholder.com/750x500/2196F3/FFFFFF?text=温馨二居室'
        ],
        location: {
          latitude: 39.918823,
          longitude: 116.407470,
          address: '北京市朝阳区三里屯街道'
        },
        price: 5800,
        type: '二居室',
        config: ['空调', '洗衣机', '冰箱', '热水器', '宽带', '电视', '沙发'],
        contact: {
          phone: '13800138002',
          wechat: 'wechat456'
        },
        owner_id: 'test_user_id',
        status: 'approved',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        title: '合租房间，价格实惠',
        desc: '合租房间，室友友好，价格实惠，适合年轻人居住。交通便利，生活设施齐全。',
        images: [
          'https://via.placeholder.com/750x500/FF9800/FFFFFF?text=合租房间'
        ],
        location: {
          latitude: 39.928823,
          longitude: 116.417470,
          address: '北京市海淀区中关村大街'
        },
        price: 2200,
        type: '合租',
        config: ['空调', '洗衣机', '冰箱', '宽带', '独立卫生间'],
        contact: {
          phone: '13800138003',
          wechat: 'wechat789'
        },
        owner_id: 'test_user_id',
        status: 'approved',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        title: '待审核房源示例',
        desc: '这是一个待审核状态的房源示例，用于测试审核功能。',
        images: [
          'https://via.placeholder.com/750x500/9C27B0/FFFFFF?text=待审核房源'
        ],
        location: {
          latitude: 39.938823,
          longitude: 116.427470,
          address: '北京市西城区金融街'
        },
        price: 4200,
        type: '一居室',
        config: ['空调', '冰箱', '宽带'],
        contact: {
          phone: '13800138004',
          wechat: 'wechat000'
        },
        owner_id: 'test_user_id',
        status: 'pending',
        created_at: new Date(),
        updated_at: new Date()
      }
    ];
    
    // 检查测试房源是否已存在
    const existHouses = await houseCollection.where({ owner_id: 'test_user_id' }).get();
    if (existHouses.data.length === 0) {
      for (const house of testHouses) {
        const houseResult = await houseCollection.add(house);
        console.log('房源创建成功，ID:', houseResult.id);
      }
      console.log('测试房源创建成功');
    } else {
      console.log('测试房源已存在，数量:', existHouses.data.length);
    }
    
    // 获取统计信息
    const userCount = await userCollection.count();
    const houseCount = await houseCollection.count();
    const approvedHouseCount = await houseCollection.where({ status: 'approved' }).count();
    const pendingHouseCount = await houseCollection.where({ status: 'pending' }).count();
    
    console.log('数据库初始化完成！');
    console.log('用户总数:', userCount.total);
    console.log('房源总数:', houseCount.total);
    console.log('已审核房源:', approvedHouseCount.total);
    console.log('待审核房源:', pendingHouseCount.total);
    
    return {
      code: 200,
      message: '数据库初始化成功',
      data: {
        userCount: userCount.total,
        houseCount: houseCount.total,
        approvedHouseCount: approvedHouseCount.total,
        pendingHouseCount: pendingHouseCount.total
      }
    };
    
  } catch (error) {
    console.error('数据库初始化失败:', error);
    return {
      code: 500,
      message: '数据库初始化失败',
      error: error.message
    };
  }
};
