'use strict';

/**
 * 数据库测试云函数
 * 用于测试数据库连接和集合创建
 */

exports.main = async (event, context) => {
  const db = uniCloud.database();
  
  try {
    console.log('开始测试数据库连接...');
    
    // 测试数据库连接
    const testResult = {
      success: true,
      message: '数据库测试成功',
      collections: {},
      errors: []
    };
    
    // 测试 user 集合
    try {
      console.log('测试 user 集合...');
      const userCollection = db.collection('user');
      
      // 尝试查询（如果集合不存在会自动创建）
      const userCount = await userCollection.count();
      testResult.collections.user = {
        exists: true,
        count: userCount.total,
        message: 'user 集合正常'
      };
      console.log('user 集合测试成功，记录数:', userCount.total);
      
    } catch (error) {
      console.error('user 集合测试失败:', error);
      testResult.collections.user = {
        exists: false,
        error: error.message,
        message: 'user 集合访问失败'
      };
      testResult.errors.push('user: ' + error.message);
    }
    
    // 测试 house 集合
    try {
      console.log('测试 house 集合...');
      const houseCollection = db.collection('house');
      
      // 尝试查询（如果集合不存在会自动创建）
      const houseCount = await houseCollection.count();
      testResult.collections.house = {
        exists: true,
        count: houseCount.total,
        message: 'house 集合正常'
      };
      console.log('house 集合测试成功，记录数:', houseCount.total);
      
    } catch (error) {
      console.error('house 集合测试失败:', error);
      testResult.collections.house = {
        exists: false,
        error: error.message,
        message: 'house 集合访问失败'
      };
      testResult.errors.push('house: ' + error.message);
    }
    
    // 如果集合为空，尝试插入测试数据
    if (testResult.collections.user && testResult.collections.user.count === 0) {
      try {
        console.log('插入测试用户...');
        const userCollection = db.collection('user');
        await userCollection.add({
          username: 'testuser',
          password: 'e10adc3949ba59abbe56e057f20f883e', // 123456 的 MD5
          nickname: '测试用户',
          avatar: '',
          phone: '13800138000',
          is_banned: false,
          created_at: new Date(),
          favorites: []
        });
        testResult.collections.user.count = 1;
        testResult.collections.user.message += '，已插入测试数据';
        console.log('测试用户插入成功');
      } catch (error) {
        console.error('插入测试用户失败:', error);
        testResult.errors.push('插入用户失败: ' + error.message);
      }
    }
    
    if (testResult.collections.house && testResult.collections.house.count === 0) {
      try {
        console.log('插入测试房源...');
        const houseCollection = db.collection('house');
        await houseCollection.add({
          title: '测试房源',
          desc: '这是一个测试房源，用于验证数据库功能',
          images: ['https://via.placeholder.com/750x500/4CAF50/FFFFFF?text=测试房源'],
          location: {
            latitude: 39.908823,
            longitude: 116.397470,
            address: '北京市朝阳区测试地址'
          },
          price: 3000,
          type: '一居室',
          config: ['空调', '洗衣机'],
          contact: {
            phone: '13800138000',
            wechat: 'test123'
          },
          owner_id: 'test_user_id',
          status: 'approved',
          created_at: new Date(),
          updated_at: new Date()
        });
        testResult.collections.house.count = 1;
        testResult.collections.house.message += '，已插入测试数据';
        console.log('测试房源插入成功');
      } catch (error) {
        console.error('插入测试房源失败:', error);
        testResult.errors.push('插入房源失败: ' + error.message);
      }
    }
    
    // 判断整体测试结果
    if (testResult.errors.length > 0) {
      testResult.success = false;
      testResult.message = '数据库测试部分失败';
    }
    
    console.log('数据库测试完成');
    return {
      code: testResult.success ? 200 : 500,
      message: testResult.message,
      data: testResult
    };
    
  } catch (error) {
    console.error('数据库测试失败:', error);
    return {
      code: 500,
      message: '数据库测试失败',
      error: error.message,
      details: '请检查 UniCloud 服务空间配置和数据库权限'
    };
  }
};
