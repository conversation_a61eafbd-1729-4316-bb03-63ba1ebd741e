'use strict';

// 验证token（复用用户认证的逻辑）
function verifyToken(token) {
  try {
    const payload = JSON.parse(Buffer.from(token, 'base64').toString());
    if (Date.now() - payload.timestamp > 24 * 60 * 60 * 1000) {
      return null;
    }
    return payload.userId;
  } catch (error) {
    return null;
  }
}

exports.main = async (event, context) => {
  const { action, data } = event;
  const db = uniCloud.database();
  const houseCollection = db.collection('house');
  const userCollection = db.collection('user');
  
  try {
    switch (action) {
      case 'getHouseList':
        return await getHouseList(houseCollection, data);
      case 'getHouseDetail':
        return await getHouseDetail(houseCollection, data);
      case 'publishHouse':
        return await publishHouse(houseCollection, data);
      case 'updateHouse':
        return await updateHouse(houseCollection, data);
      case 'deleteHouse':
        return await deleteHouse(houseCollection, data);
      case 'getMyHouses':
        return await getMyHouses(houseCollection, data);
      case 'searchHouses':
        return await searchHouses(houseCollection, data);
      default:
        return {
          code: 400,
          message: '无效的操作类型'
        };
    }
  } catch (error) {
    console.error('房源管理云函数错误:', error);
    return {
      code: 500,
      message: '服务器内部错误',
      error: error.message
    };
  }
};

// 获取房源列表
async function getHouseList(houseCollection, data) {
  const db = uniCloud.database();
  const {
    page = 1,
    pageSize = 10,
    priceMin,
    priceMax,
    type,
    location,
    sortBy = 'created_at',
    sortOrder = 'desc'
  } = data;

  let query = houseCollection.where({ status: 'approved' });

  // 价格筛选
  if (priceMin !== undefined || priceMax !== undefined) {
    if (priceMin !== undefined && priceMax !== undefined) {
      query = query.where({
        price: db.command.gte(priceMin).and(db.command.lte(priceMax))
      });
    } else if (priceMin !== undefined) {
      query = query.where({
        price: db.command.gte(priceMin)
      });
    } else if (priceMax !== undefined) {
      query = query.where({
        price: db.command.lte(priceMax)
      });
    }
  }
  
  // 房型筛选
  if (type) {
    query = query.where({ type });
  }
  
  // 地址筛选（模糊匹配）
  if (location) {
    query = query.where({
      'location.address': new RegExp(location, 'i')
    });
  }
  
  // 排序
  query = query.orderBy(sortBy, sortOrder);
  
  // 分页
  const skip = (page - 1) * pageSize;
  query = query.skip(skip).limit(pageSize);
  
  const result = await query.get();
  
  // 获取总数
  const countResult = await houseCollection.where({ status: 'approved' }).count();
  
  return {
    code: 200,
    message: '获取成功',
    data: {
      list: result.data,
      total: countResult.total,
      page,
      pageSize,
      totalPages: Math.ceil(countResult.total / pageSize)
    }
  };
}

// 获取房源详情
async function getHouseDetail(houseCollection, data) {
  const { houseId } = data;
  
  if (!houseId) {
    return {
      code: 400,
      message: '房源ID不能为空'
    };
  }
  
  const result = await houseCollection.doc(houseId).get();
  
  if (result.data.length === 0) {
    return {
      code: 404,
      message: '房源不存在'
    };
  }
  
  const house = result.data[0];
  
  // 只有已审核通过的房源或房源所有者才能查看详情
  if (house.status !== 'approved') {
    return {
      code: 403,
      message: '房源暂不可查看'
    };
  }
  
  return {
    code: 200,
    message: '获取成功',
    data: house
  };
}

// 发布房源
async function publishHouse(houseCollection, data) {
  const { token, houseData } = data;
  
  if (!token) {
    return {
      code: 401,
      message: '请先登录'
    };
  }
  
  const userId = verifyToken(token);
  if (!userId) {
    return {
      code: 401,
      message: 'token无效或已过期'
    };
  }
  
  // 验证必填字段
  const { title, price, location, contact } = houseData;
  if (!title || !price || !location || !contact) {
    return {
      code: 400,
      message: '标题、价格、位置和联系方式为必填项'
    };
  }
  
  if (!location.address) {
    return {
      code: 400,
      message: '详细地址不能为空'
    };
  }
  
  if (!contact.phone && !contact.wechat) {
    return {
      code: 400,
      message: '至少需要提供一种联系方式'
    };
  }
  
  // 构建房源数据
  const newHouse = {
    title,
    desc: houseData.desc || '',
    images: houseData.images || [],
    location,
    price: parseInt(price),
    type: houseData.type || '其他',
    config: houseData.config || [],
    contact,
    owner_id: userId,
    status: 'pending', // 待审核
    created_at: new Date(),
    updated_at: new Date()
  };
  
  const result = await houseCollection.add(newHouse);
  
  if (result.id) {
    return {
      code: 200,
      message: '发布成功，等待审核',
      data: {
        houseId: result.id
      }
    };
  } else {
    return {
      code: 500,
      message: '发布失败'
    };
  }
}

// 更新房源
async function updateHouse(houseCollection, data) {
  const { token, houseId, houseData } = data;
  
  if (!token) {
    return {
      code: 401,
      message: '请先登录'
    };
  }
  
  const userId = verifyToken(token);
  if (!userId) {
    return {
      code: 401,
      message: 'token无效或已过期'
    };
  }
  
  if (!houseId) {
    return {
      code: 400,
      message: '房源ID不能为空'
    };
  }
  
  // 检查房源是否存在且属于当前用户
  const houseResult = await houseCollection.doc(houseId).get();
  if (houseResult.data.length === 0) {
    return {
      code: 404,
      message: '房源不存在'
    };
  }
  
  const house = houseResult.data[0];
  if (house.owner_id !== userId) {
    return {
      code: 403,
      message: '无权限修改此房源'
    };
  }
  
  // 构建更新数据
  const updateData = {
    updated_at: new Date()
  };
  
  if (houseData.title !== undefined) updateData.title = houseData.title;
  if (houseData.desc !== undefined) updateData.desc = houseData.desc;
  if (houseData.images !== undefined) updateData.images = houseData.images;
  if (houseData.location !== undefined) updateData.location = houseData.location;
  if (houseData.price !== undefined) updateData.price = parseInt(houseData.price);
  if (houseData.type !== undefined) updateData.type = houseData.type;
  if (houseData.config !== undefined) updateData.config = houseData.config;
  if (houseData.contact !== undefined) updateData.contact = houseData.contact;
  
  // 如果修改了关键信息，重新设置为待审核状态
  if (houseData.title || houseData.price || houseData.location || houseData.images) {
    updateData.status = 'pending';
  }
  
  const result = await houseCollection.doc(houseId).update(updateData);
  
  if (result.updated > 0) {
    return {
      code: 200,
      message: '更新成功'
    };
  } else {
    return {
      code: 500,
      message: '更新失败'
    };
  }
}

// 删除房源
async function deleteHouse(houseCollection, data) {
  const { token, houseId } = data;
  
  if (!token) {
    return {
      code: 401,
      message: '请先登录'
    };
  }
  
  const userId = verifyToken(token);
  if (!userId) {
    return {
      code: 401,
      message: 'token无效或已过期'
    };
  }
  
  if (!houseId) {
    return {
      code: 400,
      message: '房源ID不能为空'
    };
  }
  
  // 检查房源是否存在且属于当前用户
  const houseResult = await houseCollection.doc(houseId).get();
  if (houseResult.data.length === 0) {
    return {
      code: 404,
      message: '房源不存在'
    };
  }
  
  const house = houseResult.data[0];
  if (house.owner_id !== userId) {
    return {
      code: 403,
      message: '无权限删除此房源'
    };
  }
  
  const result = await houseCollection.doc(houseId).remove();
  
  if (result.deleted > 0) {
    return {
      code: 200,
      message: '删除成功'
    };
  } else {
    return {
      code: 500,
      message: '删除失败'
    };
  }
}

// 获取我的房源
async function getMyHouses(houseCollection, data) {
  const { token, page = 1, pageSize = 10 } = data;
  
  if (!token) {
    return {
      code: 401,
      message: '请先登录'
    };
  }
  
  const userId = verifyToken(token);
  if (!userId) {
    return {
      code: 401,
      message: 'token无效或已过期'
    };
  }
  
  const skip = (page - 1) * pageSize;
  const result = await houseCollection
    .where({ owner_id: userId })
    .orderBy('created_at', 'desc')
    .skip(skip)
    .limit(pageSize)
    .get();
  
  const countResult = await houseCollection.where({ owner_id: userId }).count();
  
  return {
    code: 200,
    message: '获取成功',
    data: {
      list: result.data,
      total: countResult.total,
      page,
      pageSize,
      totalPages: Math.ceil(countResult.total / pageSize)
    }
  };
}

// 搜索房源
async function searchHouses(houseCollection, data) {
  const { keyword, page = 1, pageSize = 10 } = data;
  
  if (!keyword) {
    return {
      code: 400,
      message: '搜索关键词不能为空'
    };
  }
  
  const skip = (page - 1) * pageSize;
  
  // 在标题和描述中搜索关键词
  const result = await houseCollection
    .where({
      status: 'approved',
      $or: [
        { title: new RegExp(keyword, 'i') },
        { desc: new RegExp(keyword, 'i') },
        { 'location.address': new RegExp(keyword, 'i') }
      ]
    })
    .orderBy('created_at', 'desc')
    .skip(skip)
    .limit(pageSize)
    .get();
  
  return {
    code: 200,
    message: '搜索成功',
    data: {
      list: result.data,
      keyword,
      page,
      pageSize
    }
  };
}
