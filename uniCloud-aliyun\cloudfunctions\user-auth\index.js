'use strict';

const crypto = require('crypto');

// 密码加密函数
function hashPassword(password) {
  return crypto.createHash('sha256').update(password).digest('hex');
}

// 生成简单的JWT token（实际项目中建议使用更安全的方案）
function generateToken(userId) {
  const payload = {
    userId: userId,
    timestamp: Date.now()
  };
  return Buffer.from(JSON.stringify(payload)).toString('base64');
}

// 验证token
function verifyToken(token) {
  try {
    const payload = JSON.parse(Buffer.from(token, 'base64').toString());
    // 简单验证：token有效期24小时
    if (Date.now() - payload.timestamp > 24 * 60 * 60 * 1000) {
      return null;
    }
    return payload.userId;
  } catch (error) {
    return null;
  }
}

exports.main = async (event, context) => {
  const { action, data } = event;
  const db = uniCloud.database();
  const userCollection = db.collection('user');
  
  try {
    switch (action) {
      case 'register':
        return await register(userCollection, data);
      case 'login':
        return await login(userCollection, data);
      case 'getUserInfo':
        return await getUserInfo(userCollection, data);
      case 'updateProfile':
        return await updateProfile(userCollection, data);
      case 'changePassword':
        return await changePassword(userCollection, data);
      default:
        return {
          code: 400,
          message: '无效的操作类型'
        };
    }
  } catch (error) {
    console.error('用户认证云函数错误:', error);
    return {
      code: 500,
      message: '服务器内部错误',
      error: error.message
    };
  }
};

// 用户注册
async function register(userCollection, data) {
  const { username, password, nickname, phone } = data;
  
  // 参数验证
  if (!username || !password) {
    return {
      code: 400,
      message: '用户名和密码不能为空'
    };
  }
  
  if (username.length < 3 || username.length > 20) {
    return {
      code: 400,
      message: '用户名长度必须在3-20个字符之间'
    };
  }
  
  if (password.length < 6) {
    return {
      code: 400,
      message: '密码长度不能少于6位'
    };
  }
  
  // 检查用户名是否已存在
  const existUser = await userCollection.where({ username }).get();
  if (existUser.data.length > 0) {
    return {
      code: 400,
      message: '用户名已存在'
    };
  }
  
  // 创建新用户
  const hashedPassword = hashPassword(password);
  const newUser = {
    username,
    password: hashedPassword,
    nickname: nickname || username,
    phone: phone || '',
    avatar: '',
    is_banned: false,
    created_at: new Date(),
    favorites: []
  };
  
  const result = await userCollection.add(newUser);
  
  if (result.id) {
    const token = generateToken(result.id);
    return {
      code: 200,
      message: '注册成功',
      data: {
        userId: result.id,
        username,
        nickname: newUser.nickname,
        token
      }
    };
  } else {
    return {
      code: 500,
      message: '注册失败'
    };
  }
}

// 用户登录
async function login(userCollection, data) {
  const { username, password } = data;
  
  if (!username || !password) {
    return {
      code: 400,
      message: '用户名和密码不能为空'
    };
  }
  
  // 查找用户
  const userResult = await userCollection.where({ username }).get();
  
  if (userResult.data.length === 0) {
    return {
      code: 400,
      message: '用户不存在'
    };
  }
  
  const user = userResult.data[0];
  
  // 检查用户是否被封禁
  if (user.is_banned) {
    return {
      code: 403,
      message: '账号已被封禁'
    };
  }
  
  // 验证密码
  const hashedPassword = hashPassword(password);
  if (user.password !== hashedPassword) {
    return {
      code: 400,
      message: '密码错误'
    };
  }
  
  // 生成token
  const token = generateToken(user._id);
  
  return {
    code: 200,
    message: '登录成功',
    data: {
      userId: user._id,
      username: user.username,
      nickname: user.nickname,
      avatar: user.avatar,
      phone: user.phone,
      token
    }
  };
}

// 获取用户信息
async function getUserInfo(userCollection, data) {
  const { token } = data;
  
  if (!token) {
    return {
      code: 401,
      message: '请先登录'
    };
  }
  
  const userId = verifyToken(token);
  if (!userId) {
    return {
      code: 401,
      message: 'token无效或已过期'
    };
  }
  
  const userResult = await userCollection.doc(userId).get();
  
  if (userResult.data.length === 0) {
    return {
      code: 404,
      message: '用户不存在'
    };
  }
  
  const user = userResult.data[0];
  
  return {
    code: 200,
    message: '获取成功',
    data: {
      userId: user._id,
      username: user.username,
      nickname: user.nickname,
      avatar: user.avatar,
      phone: user.phone,
      favorites: user.favorites || [],
      created_at: user.created_at
    }
  };
}

// 更新用户资料
async function updateProfile(userCollection, data) {
  const { token, nickname, avatar, phone } = data;
  
  if (!token) {
    return {
      code: 401,
      message: '请先登录'
    };
  }
  
  const userId = verifyToken(token);
  if (!userId) {
    return {
      code: 401,
      message: 'token无效或已过期'
    };
  }
  
  const updateData = {};
  if (nickname !== undefined) updateData.nickname = nickname;
  if (avatar !== undefined) updateData.avatar = avatar;
  if (phone !== undefined) updateData.phone = phone;
  
  if (Object.keys(updateData).length === 0) {
    return {
      code: 400,
      message: '没有要更新的数据'
    };
  }
  
  const result = await userCollection.doc(userId).update(updateData);
  
  if (result.updated > 0) {
    return {
      code: 200,
      message: '更新成功'
    };
  } else {
    return {
      code: 500,
      message: '更新失败'
    };
  }
}

// 修改密码
async function changePassword(userCollection, data) {
  const { token, oldPassword, newPassword } = data;
  
  if (!token) {
    return {
      code: 401,
      message: '请先登录'
    };
  }
  
  if (!oldPassword || !newPassword) {
    return {
      code: 400,
      message: '旧密码和新密码不能为空'
    };
  }
  
  if (newPassword.length < 6) {
    return {
      code: 400,
      message: '新密码长度不能少于6位'
    };
  }
  
  const userId = verifyToken(token);
  if (!userId) {
    return {
      code: 401,
      message: 'token无效或已过期'
    };
  }
  
  // 获取用户信息验证旧密码
  const userResult = await userCollection.doc(userId).get();
  if (userResult.data.length === 0) {
    return {
      code: 404,
      message: '用户不存在'
    };
  }
  
  const user = userResult.data[0];
  const hashedOldPassword = hashPassword(oldPassword);
  
  if (user.password !== hashedOldPassword) {
    return {
      code: 400,
      message: '旧密码错误'
    };
  }
  
  // 更新密码
  const hashedNewPassword = hashPassword(newPassword);
  const result = await userCollection.doc(userId).update({
    password: hashedNewPassword
  });
  
  if (result.updated > 0) {
    return {
      code: 200,
      message: '密码修改成功'
    };
  } else {
    return {
      code: 500,
      message: '密码修改失败'
    };
  }
}
