'use strict';

// 验证token
function verifyToken(token) {
  try {
    const payload = JSON.parse(Buffer.from(token, 'base64').toString());
    if (Date.now() - payload.timestamp > 24 * 60 * 60 * 1000) {
      return null;
    }
    return payload.userId;
  } catch (error) {
    return null;
  }
}

exports.main = async (event, context) => {
  const { action, data } = event;
  const db = uniCloud.database();
  const userCollection = db.collection('user');
  const houseCollection = db.collection('house');
  
  try {
    switch (action) {
      case 'addFavorite':
        return await addFavorite(userCollection, houseCollection, data);
      case 'removeFavorite':
        return await removeFavorite(userCollection, data);
      case 'getFavorites':
        return await getFavorites(userCollection, houseCollection, data);
      case 'checkFavorite':
        return await checkFavorite(userCollection, data);
      default:
        return {
          code: 400,
          message: '无效的操作类型'
        };
    }
  } catch (error) {
    console.error('用户收藏云函数错误:', error);
    return {
      code: 500,
      message: '服务器内部错误',
      error: error.message
    };
  }
};

// 添加收藏
async function addFavorite(userCollection, houseCollection, data) {
  const { token, houseId } = data;
  
  if (!token) {
    return {
      code: 401,
      message: '请先登录'
    };
  }
  
  const userId = verifyToken(token);
  if (!userId) {
    return {
      code: 401,
      message: 'token无效或已过期'
    };
  }
  
  if (!houseId) {
    return {
      code: 400,
      message: '房源ID不能为空'
    };
  }
  
  // 检查房源是否存在且已审核通过
  const houseResult = await houseCollection.doc(houseId).get();
  if (houseResult.data.length === 0) {
    return {
      code: 404,
      message: '房源不存在'
    };
  }
  
  const house = houseResult.data[0];
  if (house.status !== 'approved') {
    return {
      code: 400,
      message: '只能收藏已审核通过的房源'
    };
  }
  
  // 获取用户当前收藏列表
  const userResult = await userCollection.doc(userId).get();
  if (userResult.data.length === 0) {
    return {
      code: 404,
      message: '用户不存在'
    };
  }
  
  const user = userResult.data[0];
  const favorites = user.favorites || [];
  
  // 检查是否已经收藏
  if (favorites.includes(houseId)) {
    return {
      code: 400,
      message: '已经收藏过此房源'
    };
  }
  
  // 添加到收藏列表
  favorites.push(houseId);
  
  const result = await userCollection.doc(userId).update({
    favorites: favorites
  });
  
  if (result.updated > 0) {
    return {
      code: 200,
      message: '收藏成功'
    };
  } else {
    return {
      code: 500,
      message: '收藏失败'
    };
  }
}

// 取消收藏
async function removeFavorite(userCollection, data) {
  const { token, houseId } = data;
  
  if (!token) {
    return {
      code: 401,
      message: '请先登录'
    };
  }
  
  const userId = verifyToken(token);
  if (!userId) {
    return {
      code: 401,
      message: 'token无效或已过期'
    };
  }
  
  if (!houseId) {
    return {
      code: 400,
      message: '房源ID不能为空'
    };
  }
  
  // 获取用户当前收藏列表
  const userResult = await userCollection.doc(userId).get();
  if (userResult.data.length === 0) {
    return {
      code: 404,
      message: '用户不存在'
    };
  }
  
  const user = userResult.data[0];
  const favorites = user.favorites || [];
  
  // 检查是否已收藏
  const index = favorites.indexOf(houseId);
  if (index === -1) {
    return {
      code: 400,
      message: '未收藏此房源'
    };
  }
  
  // 从收藏列表中移除
  favorites.splice(index, 1);
  
  const result = await userCollection.doc(userId).update({
    favorites: favorites
  });
  
  if (result.updated > 0) {
    return {
      code: 200,
      message: '取消收藏成功'
    };
  } else {
    return {
      code: 500,
      message: '取消收藏失败'
    };
  }
}

// 获取收藏列表
async function getFavorites(userCollection, houseCollection, data) {
  const { token, page = 1, pageSize = 10 } = data;
  
  if (!token) {
    return {
      code: 401,
      message: '请先登录'
    };
  }
  
  const userId = verifyToken(token);
  if (!userId) {
    return {
      code: 401,
      message: 'token无效或已过期'
    };
  }
  
  // 获取用户收藏列表
  const userResult = await userCollection.doc(userId).get();
  if (userResult.data.length === 0) {
    return {
      code: 404,
      message: '用户不存在'
    };
  }
  
  const user = userResult.data[0];
  const favorites = user.favorites || [];
  
  if (favorites.length === 0) {
    return {
      code: 200,
      message: '获取成功',
      data: {
        list: [],
        total: 0,
        page,
        pageSize,
        totalPages: 0
      }
    };
  }
  
  // 分页处理收藏ID列表
  const total = favorites.length;
  const totalPages = Math.ceil(total / pageSize);
  const start = (page - 1) * pageSize;
  const end = start + pageSize;
  const pageIds = favorites.slice(start, end);
  
  // 获取收藏的房源详情
  const houseResult = await houseCollection.where({
    _id: db.command.in(pageIds),
    status: 'approved' // 只显示已审核通过的房源
  }).get();
  
  // 按照收藏顺序排序
  const houseMap = {};
  houseResult.data.forEach(house => {
    houseMap[house._id] = house;
  });
  
  const sortedHouses = pageIds.map(id => houseMap[id]).filter(house => house);
  
  return {
    code: 200,
    message: '获取成功',
    data: {
      list: sortedHouses,
      total,
      page,
      pageSize,
      totalPages
    }
  };
}

// 检查是否已收藏
async function checkFavorite(userCollection, data) {
  const { token, houseId } = data;
  
  if (!token) {
    return {
      code: 401,
      message: '请先登录'
    };
  }
  
  const userId = verifyToken(token);
  if (!userId) {
    return {
      code: 401,
      message: 'token无效或已过期'
    };
  }
  
  if (!houseId) {
    return {
      code: 400,
      message: '房源ID不能为空'
    };
  }
  
  // 获取用户收藏列表
  const userResult = await userCollection.doc(userId).get();
  if (userResult.data.length === 0) {
    return {
      code: 404,
      message: '用户不存在'
    };
  }
  
  const user = userResult.data[0];
  const favorites = user.favorites || [];
  const isFavorited = favorites.includes(houseId);
  
  return {
    code: 200,
    message: '检查成功',
    data: {
      isFavorited
    }
  };
}
