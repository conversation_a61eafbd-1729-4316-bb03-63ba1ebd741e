{"bsonType": "object", "required": ["identifier", "created_at"], "permission": {"read": "'admin' in auth.role", "create": true, "update": false, "delete": "'admin' in auth.role"}, "properties": {"_id": {"description": "请求记录ID"}, "identifier": {"bsonType": "string", "description": "请求标识符（用户ID或IP）", "title": "请求标识符"}, "ip": {"bsonType": "string", "description": "客户端IP地址", "title": "客户端IP"}, "user_id": {"bsonType": "string", "description": "用户ID（如果已登录）", "title": "用户ID"}, "created_at": {"bsonType": "timestamp", "description": "请求时间", "title": "请求时间", "forceDefaultValue": {"$env": "now"}}}}