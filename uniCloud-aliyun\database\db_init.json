{"collections": [{"collectionName": "house", "description": "房源信息集合", "schema": {"bsonType": "object", "required": ["title", "desc", "location", "price", "type", "owner_id", "status"], "properties": {"_id": {"description": "房源ID"}, "title": {"bsonType": "string", "description": "房源标题", "maxLength": 100, "minLength": 1}, "desc": {"bsonType": "string", "description": "房源简介", "maxLength": 1000}, "images": {"bsonType": "array", "description": "房源图片URL数组", "items": {"bsonType": "string"}, "maxItems": 10}, "location": {"bsonType": "object", "description": "位置信息", "required": ["address"], "properties": {"address": {"bsonType": "string", "description": "详细地址"}, "latitude": {"bsonType": "number", "description": "纬度"}, "longitude": {"bsonType": "number", "description": "经度"}, "district": {"bsonType": "string", "description": "区域"}, "subway": {"bsonType": "string", "description": "地铁站"}}}, "price": {"bsonType": "number", "description": "租金（元/月）", "minimum": 0}, "type": {"bsonType": "string", "description": "房型", "enum": ["一居", "二居", "三居", "四居", "合租", "整租", "单间"]}, "config": {"bsonType": "array", "description": "房屋配置", "items": {"bsonType": "string", "enum": ["空调", "洗衣机", "冰箱", "热水器", "电视", "WiFi", "床", "衣柜", "桌椅", "独立卫生间", "阳台", "停车位"]}}, "contact": {"bsonType": "object", "description": "联系方式", "properties": {"phone": {"bsonType": "string", "description": "电话号码"}, "wechat": {"bsonType": "string", "description": "微信号"}}}, "owner_id": {"bsonType": "string", "description": "发布用户ID"}, "status": {"bsonType": "string", "description": "审核状态", "enum": ["pending", "approved", "rejected"], "default": "pending"}, "reject_reason": {"bsonType": "string", "description": "驳回原因"}, "area": {"bsonType": "number", "description": "面积（平方米）", "minimum": 0}, "floor": {"bsonType": "string", "description": "楼层信息"}, "orientation": {"bsonType": "string", "description": "朝向", "enum": ["东", "南", "西", "北", "东南", "东北", "西南", "西北", "南北"]}, "is_furnished": {"bsonType": "bool", "description": "是否拎包入住", "default": false}, "view_count": {"bsonType": "number", "description": "浏览次数", "default": 0}, "favorite_count": {"bsonType": "number", "description": "收藏次数", "default": 0}, "created_at": {"bsonType": "timestamp", "description": "创建时间"}, "updated_at": {"bsonType": "timestamp", "description": "更新时间"}, "approved_at": {"bsonType": "timestamp", "description": "审核通过时间"}}}}, {"collectionName": "user", "description": "用户信息集合", "schema": {"bsonType": "object", "required": ["username", "password"], "properties": {"_id": {"description": "用户ID"}, "username": {"bsonType": "string", "description": "用户登录账号（唯一）", "maxLength": 50, "minLength": 3}, "password": {"bsonType": "string", "description": "哈希后的密码"}, "nickname": {"bsonType": "string", "description": "昵称", "maxLength": 50}, "avatar": {"bsonType": "string", "description": "头像URL"}, "phone": {"bsonType": "string", "description": "手机号（可选）"}, "email": {"bsonType": "string", "description": "邮箱（可选）"}, "is_banned": {"bsonType": "bool", "description": "是否被封禁", "default": false}, "role": {"bsonType": "array", "description": "用户角色", "items": {"bsonType": "string", "enum": ["user", "admin"]}, "default": ["user"]}, "last_login_at": {"bsonType": "timestamp", "description": "最后登录时间"}, "created_at": {"bsonType": "timestamp", "description": "注册时间"}, "updated_at": {"bsonType": "timestamp", "description": "更新时间"}}}}, {"collectionName": "user_favorites", "description": "用户收藏集合", "schema": {"bsonType": "object", "required": ["user_id", "house_id"], "properties": {"_id": {"description": "收藏记录ID"}, "user_id": {"bsonType": "string", "description": "用户ID"}, "house_id": {"bsonType": "string", "description": "房源ID"}, "created_at": {"bsonType": "timestamp", "description": "收藏时间"}}}}, {"collectionName": "system_config", "description": "系统配置集合", "schema": {"bsonType": "object", "required": ["key", "value"], "properties": {"_id": {"description": "配置ID"}, "key": {"bsonType": "string", "description": "配置键", "enum": ["banner_list", "announcement", "search_keywords", "contact_info"]}, "value": {"description": "配置值（可以是任意类型）"}, "description": {"bsonType": "string", "description": "配置说明"}, "updated_at": {"bsonType": "timestamp", "description": "更新时间"}}}}]}