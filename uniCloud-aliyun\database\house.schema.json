{"bsonType": "object", "required": ["title", "desc", "location", "price", "type", "owner_id", "status"], "permission": {"read": true, "create": "auth.uid != null", "update": "auth.uid == doc.owner_id || 'admin' in auth.role", "delete": "auth.uid == doc.owner_id || 'admin' in auth.role"}, "properties": {"_id": {"description": "房源ID"}, "title": {"bsonType": "string", "description": "房源标题", "maxLength": 100, "minLength": 1, "title": "房源标题"}, "desc": {"bsonType": "string", "description": "房源简介", "maxLength": 1000, "title": "房源简介"}, "images": {"bsonType": "array", "description": "房源图片URL数组", "title": "房源图片", "items": {"bsonType": "string"}, "maxItems": 10}, "location": {"bsonType": "object", "description": "位置信息", "title": "位置信息", "required": ["address"], "properties": {"address": {"bsonType": "string", "description": "详细地址", "title": "详细地址"}, "latitude": {"bsonType": "number", "description": "纬度", "title": "纬度"}, "longitude": {"bsonType": "number", "description": "经度", "title": "经度"}, "district": {"bsonType": "string", "description": "区域", "title": "区域"}, "subway": {"bsonType": "string", "description": "地铁站", "title": "地铁站"}}}, "price": {"bsonType": "number", "description": "租金（元/月）", "title": "租金", "minimum": 0}, "type": {"bsonType": "string", "description": "房型", "title": "房型", "enum": ["一居", "二居", "三居", "四居", "合租", "整租", "单间"]}, "config": {"bsonType": "array", "description": "房屋配置", "title": "房屋配置", "items": {"bsonType": "string", "enum": ["空调", "洗衣机", "冰箱", "热水器", "电视", "WiFi", "床", "衣柜", "桌椅", "独立卫生间", "阳台", "停车位"]}}, "contact": {"bsonType": "object", "description": "联系方式", "title": "联系方式", "properties": {"phone": {"bsonType": "string", "description": "电话号码", "title": "电话号码"}, "wechat": {"bsonType": "string", "description": "微信号", "title": "微信号"}}}, "owner_id": {"bsonType": "string", "description": "发布用户ID", "title": "发布用户ID", "forceDefaultValue": {"$env": "uid"}}, "status": {"bsonType": "string", "description": "审核状态", "title": "审核状态", "enum": ["pending", "approved", "rejected"], "defaultValue": "pending"}, "reject_reason": {"bsonType": "string", "description": "驳回原因", "title": "驳回原因"}, "area": {"bsonType": "number", "description": "面积（平方米）", "title": "面积", "minimum": 0}, "floor": {"bsonType": "string", "description": "楼层信息", "title": "楼层"}, "orientation": {"bsonType": "string", "description": "朝向", "title": "朝向", "enum": ["东", "南", "西", "北", "东南", "东北", "西南", "西北", "南北"]}, "is_furnished": {"bsonType": "bool", "description": "是否拎包入住", "title": "是否拎包入住", "defaultValue": false}, "view_count": {"bsonType": "number", "description": "浏览次数", "title": "浏览次数", "defaultValue": 0}, "favorite_count": {"bsonType": "number", "description": "收藏次数", "title": "收藏次数", "defaultValue": 0}, "created_at": {"bsonType": "timestamp", "description": "创建时间", "title": "创建时间", "forceDefaultValue": {"$env": "now"}}, "updated_at": {"bsonType": "timestamp", "description": "更新时间", "title": "更新时间", "forceDefaultValue": {"$env": "now"}}, "approved_at": {"bsonType": "timestamp", "description": "审核通过时间", "title": "审核通过时间"}}}