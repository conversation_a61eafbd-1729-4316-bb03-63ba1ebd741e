{"house": [{"IndexName": "status_created_at", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "status", "Direction": "1"}, {"Name": "created_at", "Direction": "-1"}], "MgoIsUnique": false}}, {"IndexName": "owner_id_status", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "owner_id", "Direction": "1"}, {"Name": "status", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "location_district", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "location.district", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "price_type", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "price", "Direction": "1"}, {"Name": "type", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "title_text", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "title", "Direction": "text"}, {"Name": "desc", "Direction": "text"}, {"Name": "location.address", "Direction": "text"}], "MgoIsUnique": false}}], "user": [{"IndexName": "username_unique", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "username", "Direction": "1"}], "MgoIsUnique": true}}, {"IndexName": "phone_unique", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "phone", "Direction": "1"}], "MgoIsUnique": true, "MgoSparse": true}}, {"IndexName": "email_unique", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "email", "Direction": "1"}], "MgoIsUnique": true, "MgoSparse": true}}], "user_favorites": [{"IndexName": "user_house_unique", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "user_id", "Direction": "1"}, {"Name": "house_id", "Direction": "1"}], "MgoIsUnique": true}}, {"IndexName": "user_id_created_at", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "user_id", "Direction": "1"}, {"Name": "created_at", "Direction": "-1"}], "MgoIsUnique": false}}], "system_config": [{"IndexName": "key_unique", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "key", "Direction": "1"}], "MgoIsUnique": true}}]}