{"bsonType": "object", "required": ["key", "value"], "permission": {"read": true, "create": "'admin' in auth.role", "update": "'admin' in auth.role", "delete": "'admin' in auth.role"}, "properties": {"_id": {"description": "配置ID"}, "key": {"bsonType": "string", "description": "配置键", "title": "配置键", "enum": ["banner_list", "announcement", "search_keywords", "contact_info"]}, "value": {"description": "配置值（可以是任意类型）", "title": "配置值"}, "description": {"bsonType": "string", "description": "配置说明", "title": "配置说明"}, "updated_at": {"bsonType": "timestamp", "description": "更新时间", "title": "更新时间", "forceDefaultValue": {"$env": "now"}}}}