{"bsonType": "object", "required": ["user_id", "file_id", "file_name"], "permission": {"read": "auth.uid == doc.user_id || 'admin' in auth.role", "create": "auth.uid == doc.user_id", "update": false, "delete": "auth.uid == doc.user_id || 'admin' in auth.role"}, "properties": {"_id": {"description": "上传记录ID"}, "user_id": {"bsonType": "string", "description": "上传用户ID", "title": "上传用户ID", "forceDefaultValue": {"$env": "uid"}}, "file_id": {"bsonType": "string", "description": "云存储文件ID", "title": "云存储文件ID"}, "file_name": {"bsonType": "string", "description": "原始文件名", "title": "原始文件名"}, "file_type": {"bsonType": "string", "description": "文件类型", "title": "文件类型"}, "file_size": {"bsonType": "number", "description": "文件大小（字节）", "title": "文件大小"}, "cloud_path": {"bsonType": "string", "description": "云存储路径", "title": "云存储路径"}, "file_url": {"bsonType": "string", "description": "文件访问URL", "title": "文件访问URL"}, "created_at": {"bsonType": "timestamp", "description": "上传时间", "title": "上传时间", "forceDefaultValue": {"$env": "now"}}}}