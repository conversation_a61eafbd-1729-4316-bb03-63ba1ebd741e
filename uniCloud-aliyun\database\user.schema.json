{"bsonType": "object", "required": ["username"], "permission": {"read": "auth.uid == doc._id || 'admin' in auth.role", "create": false, "update": "auth.uid == doc._id || 'admin' in auth.role", "delete": "'admin' in auth.role"}, "properties": {"_id": {"description": "用户ID"}, "username": {"bsonType": "string", "description": "用户登录账号（唯一）", "title": "用户名", "maxLength": 50, "minLength": 3}, "password": {"bsonType": "string", "description": "哈希后的密码", "title": "密码"}, "nickname": {"bsonType": "string", "description": "昵称", "title": "昵称", "maxLength": 50}, "avatar": {"bsonType": "string", "description": "头像URL", "title": "头像"}, "phone": {"bsonType": "string", "description": "手机号（可选）", "title": "手机号"}, "email": {"bsonType": "string", "description": "邮箱（可选）", "title": "邮箱"}, "is_banned": {"bsonType": "bool", "description": "是否被封禁", "title": "是否被封禁", "defaultValue": false}, "role": {"bsonType": "array", "description": "用户角色", "title": "用户角色", "items": {"bsonType": "string", "enum": ["user", "admin"]}, "defaultValue": ["user"]}, "last_login_at": {"bsonType": "timestamp", "description": "最后登录时间", "title": "最后登录时间"}, "created_at": {"bsonType": "timestamp", "description": "注册时间", "title": "注册时间", "forceDefaultValue": {"$env": "now"}}, "updated_at": {"bsonType": "timestamp", "description": "更新时间", "title": "更新时间", "forceDefaultValue": {"$env": "now"}}}}