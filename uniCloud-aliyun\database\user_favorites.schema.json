{"bsonType": "object", "required": ["user_id", "house_id"], "permission": {"read": "auth.uid == doc.user_id", "create": "auth.uid == doc.user_id", "update": false, "delete": "auth.uid == doc.user_id"}, "properties": {"_id": {"description": "收藏记录ID"}, "user_id": {"bsonType": "string", "description": "用户ID", "title": "用户ID", "forceDefaultValue": {"$env": "uid"}}, "house_id": {"bsonType": "string", "description": "房源ID", "title": "房源ID"}, "created_at": {"bsonType": "timestamp", "description": "收藏时间", "title": "收藏时间", "forceDefaultValue": {"$env": "now"}}}}