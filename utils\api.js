/**
 * API 服务模块
 * 封装与后端云函数的交互逻辑
 */

// API 配置
const API_CONFIG = {
  // 云函数名称
  FUNCTION_NAME: 'api-router',
  // 请求超时时间（毫秒）
  TIMEOUT: 30000,
  // 重试次数
  RETRY_COUNT: 3,
  // 重试延迟（毫秒）
  RETRY_DELAY: 1000
}

/**
 * API 服务类
 */
class ApiService {
  constructor() {
    this.baseConfig = {
      name: API_CONFIG.FUNCTION_NAME,
      timeout: API_CONFIG.TIMEOUT
    }
  }

  /**
   * 发送请求
   * @param {string} method - HTTP 方法
   * @param {string} path - API 路径
   * @param {object} data - 请求数据
   * @param {object} options - 请求选项
   * @returns {Promise} 请求结果
   */
  async request(method, path, data = {}, options = {}) {
    const requestData = {
      httpMethod: method.toUpperCase(),
      path: path,
      ...data
    }

    // 如果是 GET 请求，将数据放到 queryStringParameters
    if (method.toUpperCase() === 'GET') {
      requestData.queryStringParameters = data
    } else {
      // 其他请求方法将数据放到 body
      requestData.body = JSON.stringify(data)
    }

    const config = {
      ...this.baseConfig,
      data: requestData,
      ...options
    }

    try {
      const result = await this._callWithRetry(config)
      return this._handleResponse(result)
    } catch (error) {
      return this._handleError(error, method, path)
    }
  }

  /**
   * GET 请求
   * @param {string} path - API 路径
   * @param {object} params - 查询参数
   * @param {object} options - 请求选项
   * @returns {Promise} 请求结果
   */
  get(path, params = {}, options = {}) {
    return this.request('GET', path, params, options)
  }

  /**
   * POST 请求
   * @param {string} path - API 路径
   * @param {object} data - 请求数据
   * @param {object} options - 请求选项
   * @returns {Promise} 请求结果
   */
  post(path, data = {}, options = {}) {
    return this.request('POST', path, data, options)
  }

  /**
   * PUT 请求
   * @param {string} path - API 路径
   * @param {object} data - 请求数据
   * @param {object} options - 请求选项
   * @returns {Promise} 请求结果
   */
  put(path, data = {}, options = {}) {
    return this.request('PUT', path, data, options)
  }

  /**
   * DELETE 请求
   * @param {string} path - API 路径
   * @param {object} data - 请求数据
   * @param {object} options - 请求选项
   * @returns {Promise} 请求结果
   */
  delete(path, data = {}, options = {}) {
    return this.request('DELETE', path, data, options)
  }

  /**
   * 带重试的云函数调用
   * @param {object} config - 请求配置
   * @param {number} retryCount - 重试次数
   * @returns {Promise} 请求结果
   */
  async _callWithRetry(config, retryCount = API_CONFIG.RETRY_COUNT) {
    try {
      const result = await uniCloud.callFunction(config)
      return result
    } catch (error) {
      console.error(`API 请求失败 (剩余重试次数: ${retryCount}):`, error)
      
      if (retryCount > 0 && this._shouldRetry(error)) {
        // 等待一段时间后重试
        await this._delay(API_CONFIG.RETRY_DELAY)
        return this._callWithRetry(config, retryCount - 1)
      }
      
      throw error
    }
  }

  /**
   * 判断是否应该重试
   * @param {Error} error - 错误对象
   * @returns {boolean} 是否应该重试
   */
  _shouldRetry(error) {
    // 网络错误或超时错误可以重试
    const retryableErrors = [
      'NETWORK_ERROR',
      'TIMEOUT',
      'CONNECTION_ERROR',
      'FUNCTION_TIMEOUT'
    ]
    
    return retryableErrors.some(errorType => 
      error.message && error.message.includes(errorType)
    )
  }

  /**
   * 延迟函数
   * @param {number} ms - 延迟毫秒数
   * @returns {Promise} Promise 对象
   */
  _delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 处理响应
   * @param {object} result - 云函数返回结果
   * @returns {object} 处理后的结果
   */
  _handleResponse(result) {
    if (result.success) {
      // 解析响应体
      let responseData
      try {
        responseData = typeof result.result === 'string' 
          ? JSON.parse(result.result) 
          : result.result
      } catch (error) {
        console.error('解析响应数据失败:', error)
        return {
          code: -1,
          message: '响应数据格式错误',
          data: null
        }
      }

      return responseData
    } else {
      console.error('云函数调用失败:', result)
      return {
        code: -1,
        message: result.error?.message || '云函数调用失败',
        data: null
      }
    }
  }

  /**
   * 处理错误
   * @param {Error} error - 错误对象
   * @param {string} method - 请求方法
   * @param {string} path - 请求路径
   * @returns {object} 错误响应
   */
  _handleError(error, method, path) {
    console.error(`API 请求错误 [${method} ${path}]:`, error)
    
    let message = '网络请求失败'
    
    if (error.message) {
      if (error.message.includes('TIMEOUT')) {
        message = '请求超时，请重试'
      } else if (error.message.includes('NETWORK')) {
        message = '网络连接失败，请检查网络'
      } else if (error.message.includes('FUNCTION_NOT_FOUND')) {
        message = '接口不存在'
      } else {
        message = error.message
      }
    }

    return {
      code: -1,
      message,
      data: null
    }
  }

  /**
   * 上传文件
   * @param {string} filePath - 文件路径
   * @param {string} fileName - 文件名
   * @param {object} options - 上传选项
   * @returns {Promise} 上传结果
   */
  async uploadFile(filePath, fileName, options = {}) {
    try {
      // 获取文件信息
      const fileInfo = await this._getFileInfo(filePath)
      
      // 转换为 base64
      const base64Data = await this._fileToBase64(filePath)
      
      // 调用上传接口
      const token = uni.getStorageSync('admin_token')
      const result = await this.post('/upload/image', {
        token,
        file: base64Data,
        fileName: fileName || fileInfo.name,
        fileType: fileInfo.type
      })

      return result
    } catch (error) {
      console.error('文件上传失败:', error)
      return {
        code: -1,
        message: '文件上传失败',
        data: null
      }
    }
  }

  /**
   * 获取文件信息
   * @param {string} filePath - 文件路径
   * @returns {Promise} 文件信息
   */
  _getFileInfo(filePath) {
    return new Promise((resolve, reject) => {
      uni.getFileInfo({
        filePath,
        success: (res) => {
          const name = filePath.split('/').pop() || 'file'
          const extension = name.split('.').pop() || ''
          const type = this._getFileType(extension)
          
          resolve({
            name,
            size: res.size,
            type,
            extension
          })
        },
        fail: reject
      })
    })
  }

  /**
   * 文件转 base64
   * @param {string} filePath - 文件路径
   * @returns {Promise} base64 字符串
   */
  _fileToBase64(filePath) {
    return new Promise((resolve, reject) => {
      uni.getFileSystemManager().readFile({
        filePath,
        encoding: 'base64',
        success: (res) => resolve(res.data),
        fail: reject
      })
    })
  }

  /**
   * 根据文件扩展名获取 MIME 类型
   * @param {string} extension - 文件扩展名
   * @returns {string} MIME 类型
   */
  _getFileType(extension) {
    const typeMap = {
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      png: 'image/png',
      gif: 'image/gif',
      webp: 'image/webp',
      bmp: 'image/bmp',
      svg: 'image/svg+xml'
    }
    
    return typeMap[extension.toLowerCase()] || 'application/octet-stream'
  }
}

// 创建 API 服务实例
const apiService = new ApiService()

/**
 * 请求拦截器
 */
const requestInterceptors = []

/**
 * 响应拦截器
 */
const responseInterceptors = []

/**
 * 添加请求拦截器
 * @param {Function} interceptor - 拦截器函数
 */
export function addRequestInterceptor(interceptor) {
  requestInterceptors.push(interceptor)
}

/**
 * 添加响应拦截器
 * @param {Function} interceptor - 拦截器函数
 */
export function addResponseInterceptor(interceptor) {
  responseInterceptors.push(interceptor)
}

/**
 * 通用错误处理
 * @param {object} error - 错误对象
 */
export function handleApiError(error) {
  if (error.code === 401) {
    // 未授权，跳转到登录页
    uni.removeStorageSync('admin_token')
    uni.removeStorageSync('admin_user')
    uni.reLaunch({
      url: '/pages/login/login'
    })
  } else if (error.code === 403) {
    // 权限不足
    uni.showToast({
      title: '权限不足',
      icon: 'none'
    })
  } else if (error.code === 429) {
    // 请求过于频繁
    uni.showToast({
      title: '请求过于频繁，请稍后再试',
      icon: 'none'
    })
  } else {
    // 其他错误
    uni.showToast({
      title: error.message || '请求失败',
      icon: 'none'
    })
  }
}

// 导出 API 服务实例
export { apiService }

// 默认导出
export default apiService
