/**
 * API 接口定义
 * 统一管理所有 API 接口路径和参数
 */

import { apiService } from './api.js'

/**
 * 用户相关 API
 */
export const userApi = {
  /**
   * 用户注册
   * @param {object} data - 注册数据
   * @param {string} data.username - 用户名
   * @param {string} data.password - 密码
   * @param {string} data.nickname - 昵称（可选）
   * @param {string} data.phone - 手机号（可选）
   * @param {string} data.email - 邮箱（可选）
   */
  register(data) {
    return apiService.post('/user/register', data)
  },

  /**
   * 用户登录
   * @param {object} data - 登录数据
   * @param {string} data.username - 用户名
   * @param {string} data.password - 密码
   */
  login(data) {
    return apiService.post('/user/login', data)
  },

  /**
   * 用户登出
   * @param {string} token - 访问令牌
   */
  logout(token) {
    return apiService.post('/user/logout', { token })
  },

  /**
   * 获取用户信息
   * @param {string} token - 访问令牌
   */
  getProfile(token) {
    return apiService.get('/user/profile', { token })
  },

  /**
   * 更新用户信息
   * @param {object} data - 更新数据
   * @param {string} data.token - 访问令牌
   * @param {string} data.nickname - 昵称（可选）
   * @param {string} data.avatar - 头像（可选）
   * @param {string} data.phone - 手机号（可选）
   * @param {string} data.email - 邮箱（可选）
   */
  updateProfile(data) {
    return apiService.put('/user/profile', data)
  },

  /**
   * 修改密码
   * @param {object} data - 修改密码数据
   * @param {string} data.token - 访问令牌
   * @param {string} data.oldPassword - 旧密码
   * @param {string} data.newPassword - 新密码
   */
  changePassword(data) {
    return apiService.put('/user/password', data)
  },

  /**
   * 重置密码
   * @param {object} data - 重置密码数据
   * @param {string} data.username - 用户名（可选）
   * @param {string} data.phone - 手机号（可选）
   * @param {string} data.email - 邮箱（可选）
   * @param {string} data.newPassword - 新密码
   */
  resetPassword(data) {
    return apiService.post('/user/reset-password', data)
  }
}

/**
 * 房源相关 API
 */
export const houseApi = {
  /**
   * 获取房源列表
   * @param {object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.limit - 每页数量
   * @param {string} params.status - 状态筛选
   * @param {string} params.district - 区域筛选
   * @param {string} params.type - 房型筛选
   * @param {number} params.minPrice - 最低价格
   * @param {number} params.maxPrice - 最高价格
   * @param {array} params.config - 配置筛选
   */
  getList(params) {
    return apiService.get('/house/list', params)
  },

  /**
   * 获取房源详情
   * @param {string} id - 房源ID
   */
  getDetail(id) {
    return apiService.get(`/house/detail/${id}`)
  },

  /**
   * 搜索房源
   * @param {object} params - 搜索参数
   * @param {string} params.keyword - 搜索关键词
   * @param {number} params.page - 页码
   * @param {number} params.limit - 每页数量
   */
  search(params) {
    return apiService.get('/house/search', params)
  },

  /**
   * 创建房源
   * @param {object} data - 房源数据
   * @param {string} data.token - 访问令牌
   * @param {string} data.title - 标题
   * @param {string} data.desc - 描述
   * @param {array} data.images - 图片列表
   * @param {object} data.location - 位置信息
   * @param {number} data.price - 价格
   * @param {string} data.type - 房型
   */
  create(data) {
    return apiService.post('/house/create', data)
  },

  /**
   * 更新房源
   * @param {string} id - 房源ID
   * @param {object} data - 更新数据
   */
  update(id, data) {
    return apiService.put(`/house/update/${id}`, data)
  },

  /**
   * 删除房源
   * @param {string} id - 房源ID
   * @param {string} token - 访问令牌
   */
  delete(id, token) {
    return apiService.delete(`/house/delete/${id}`, { token })
  },

  /**
   * 获取我的房源
   * @param {object} params - 查询参数
   * @param {string} params.token - 访问令牌
   * @param {number} params.page - 页码
   * @param {number} params.limit - 每页数量
   */
  getMyHouses(params) {
    return apiService.get('/house/my-houses', params)
  }
}

/**
 * 收藏相关 API
 */
export const favoriteApi = {
  /**
   * 添加收藏
   * @param {object} data - 收藏数据
   * @param {string} data.token - 访问令牌
   * @param {string} data.house_id - 房源ID
   */
  add(data) {
    return apiService.post('/favorite/add', data)
  },

  /**
   * 取消收藏
   * @param {object} data - 取消收藏数据
   * @param {string} data.token - 访问令牌
   * @param {string} data.house_id - 房源ID
   */
  remove(data) {
    return apiService.delete('/favorite/remove', data)
  },

  /**
   * 获取收藏列表
   * @param {object} params - 查询参数
   * @param {string} params.token - 访问令牌
   * @param {number} params.page - 页码
   * @param {number} params.limit - 每页数量
   */
  getList(params) {
    return apiService.get('/favorite/list', params)
  }
}

/**
 * 文件上传相关 API
 */
export const uploadApi = {
  /**
   * 上传图片
   * @param {object} data - 上传数据
   * @param {string} data.token - 访问令牌
   * @param {string} data.file - base64编码的文件数据
   * @param {string} data.fileName - 文件名
   * @param {string} data.fileType - 文件类型
   */
  uploadImage(data) {
    return apiService.post('/upload/image', data)
  },

  /**
   * 上传文件（便捷方法）
   * @param {string} filePath - 文件路径
   * @param {string} fileName - 文件名（可选）
   */
  uploadFile(filePath, fileName) {
    return apiService.uploadFile(filePath, fileName)
  }
}

/**
 * 管理员相关 API
 */
export const adminApi = {
  /**
   * 获取用户列表
   * @param {object} params - 查询参数
   * @param {string} params.token - 访问令牌
   * @param {number} params.page - 页码
   * @param {number} params.limit - 每页数量
   * @param {string} params.keyword - 搜索关键词
   * @param {boolean} params.is_banned - 是否封禁
   */
  getUserList(params) {
    return apiService.get('/admin/users', params)
  },

  /**
   * 封禁用户
   * @param {string} id - 用户ID
   * @param {string} token - 访问令牌
   */
  banUser(id, token) {
    return apiService.put(`/admin/user/ban/${id}`, { token })
  },

  /**
   * 解封用户
   * @param {string} id - 用户ID
   * @param {string} token - 访问令牌
   */
  unbanUser(id, token) {
    return apiService.put(`/admin/user/unban/${id}`, { token })
  },

  /**
   * 获取房源列表（管理员）
   * @param {object} params - 查询参数
   * @param {string} params.token - 访问令牌
   * @param {number} params.page - 页码
   * @param {number} params.limit - 每页数量
   * @param {string} params.status - 状态筛选
   * @param {string} params.keyword - 搜索关键词
   */
  getHouseList(params) {
    return apiService.get('/admin/houses', params)
  },

  /**
   * 审核通过房源
   * @param {string} id - 房源ID
   * @param {string} token - 访问令牌
   */
  approveHouse(id, token) {
    return apiService.put(`/admin/house/approve/${id}`, { token })
  },

  /**
   * 驳回房源
   * @param {string} id - 房源ID
   * @param {object} data - 驳回数据
   * @param {string} data.token - 访问令牌
   * @param {string} data.reason - 驳回原因
   */
  rejectHouse(id, data) {
    return apiService.put(`/admin/house/reject/${id}`, data)
  },

  /**
   * 获取统计数据
   * @param {string} token - 访问令牌
   */
  getStatistics(token) {
    return apiService.get('/admin/statistics', { token })
  },

  /**
   * 获取系统配置
   * @param {string} token - 访问令牌
   */
  getSystemConfig(token) {
    return apiService.get('/admin/config', { token })
  },

  /**
   * 更新系统配置
   * @param {object} data - 配置数据
   * @param {string} data.token - 访问令牌
   * @param {string} data.key - 配置键
   * @param {any} data.value - 配置值
   * @param {string} data.description - 配置描述
   */
  updateSystemConfig(data) {
    return apiService.put('/admin/config', data)
  }
}

/**
 * 组合 API 调用
 * 用于复杂的业务逻辑
 */
export const compositeApi = {
  /**
   * 管理员登录并获取统计数据
   * @param {object} loginData - 登录数据
   */
  async adminLoginAndGetStats(loginData) {
    try {
      // 先登录
      const loginResult = await userApi.login(loginData)
      if (loginResult.code !== 0) {
        return loginResult
      }

      // 检查是否为管理员
      if (!loginResult.data.role || !loginResult.data.role.includes('admin')) {
        return {
          code: 403,
          message: '权限不足，仅限管理员登录',
          data: null
        }
      }

      // 获取统计数据
      const statsResult = await adminApi.getStatistics(loginResult.data.token)
      
      return {
        code: 0,
        message: '登录成功',
        data: {
          ...loginResult.data,
          statistics: statsResult.data
        }
      }
    } catch (error) {
      return {
        code: -1,
        message: '登录失败',
        data: null
      }
    }
  },

  /**
   * 批量上传图片
   * @param {array} filePaths - 文件路径数组
   * @param {string} token - 访问令牌
   */
  async batchUploadImages(filePaths, token) {
    const results = []
    const errors = []

    for (let i = 0; i < filePaths.length; i++) {
      try {
        const result = await uploadApi.uploadFile(filePaths[i])
        if (result.code === 0) {
          results.push(result.data)
        } else {
          errors.push({
            index: i,
            filePath: filePaths[i],
            error: result.message
          })
        }
      } catch (error) {
        errors.push({
          index: i,
          filePath: filePaths[i],
          error: error.message
        })
      }
    }

    return {
      code: 0,
      message: `成功上传 ${results.length} 张图片`,
      data: {
        success: results,
        errors: errors,
        successCount: results.length,
        errorCount: errors.length
      }
    }
  }
}

// 导出所有 API
export default {
  user: userApi,
  house: houseApi,
  favorite: favoriteApi,
  upload: uploadApi,
  admin: adminApi,
  composite: compositeApi
}
