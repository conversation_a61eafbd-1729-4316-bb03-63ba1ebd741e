/**
 * 通用工具函数
 */

/**
 * 格式化时间
 * @param {Date|string|number} time - 时间
 * @param {string} format - 格式化模板，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的时间字符串
 */
export function formatTime(time, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!time) return ''
  
  const date = new Date(time)
  if (isNaN(date.getTime())) return ''
  
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds)
}

/**
 * 格式化相对时间
 * @param {Date|string|number} time - 时间
 * @returns {string} 相对时间字符串
 */
export function formatRelativeTime(time) {
  if (!time) return ''
  
  const date = new Date(time)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  const week = 7 * day
  const month = 30 * day
  const year = 365 * day
  
  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`
  } else if (diff < week) {
    return `${Math.floor(diff / day)}天前`
  } else if (diff < month) {
    return `${Math.floor(diff / week)}周前`
  } else if (diff < year) {
    return `${Math.floor(diff / month)}个月前`
  } else {
    return `${Math.floor(diff / year)}年前`
  }
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @param {number} decimals - 小数位数，默认2
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

/**
 * 格式化数字
 * @param {number} num - 数字
 * @param {number} decimals - 小数位数，默认0
 * @returns {string} 格式化后的数字
 */
export function formatNumber(num, decimals = 0) {
  if (isNaN(num)) return '0'
  
  if (num >= 10000) {
    return (num / 10000).toFixed(decimals) + '万'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(decimals) + 'k'
  } else {
    return num.toString()
  }
}

/**
 * 格式化手机号
 * @param {string} phone - 手机号
 * @param {boolean} mask - 是否脱敏，默认true
 * @returns {string} 格式化后的手机号
 */
export function formatPhone(phone, mask = true) {
  if (!phone) return ''
  
  const cleanPhone = phone.replace(/\D/g, '')
  
  if (cleanPhone.length !== 11) return phone
  
  if (mask) {
    return cleanPhone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
  } else {
    return cleanPhone.replace(/(\d{3})(\d{4})(\d{4})/, '$1 $2 $3')
  }
}

/**
 * 格式化邮箱
 * @param {string} email - 邮箱
 * @param {boolean} mask - 是否脱敏，默认true
 * @returns {string} 格式化后的邮箱
 */
export function formatEmail(email, mask = true) {
  if (!email) return ''
  
  if (!mask) return email
  
  const [username, domain] = email.split('@')
  if (!username || !domain) return email
  
  if (username.length <= 2) {
    return email
  }
  
  const maskedUsername = username.substring(0, 2) + '*'.repeat(username.length - 2)
  return maskedUsername + '@' + domain
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间，默认300ms
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait = 300) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} limit - 限制时间，默认300ms
 * @returns {Function} 节流后的函数
 */
export function throttle(func, limit = 300) {
  let inThrottle
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 深拷贝
 * @param {any} obj - 要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime())
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}

/**
 * 生成随机字符串
 * @param {number} length - 长度，默认8
 * @param {string} chars - 字符集，默认包含字母和数字
 * @returns {string} 随机字符串
 */
export function randomString(length = 8, chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') {
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 验证邮箱格式
 * @param {string} email - 邮箱
 * @returns {boolean} 是否有效
 */
export function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 验证手机号格式
 * @param {string} phone - 手机号
 * @returns {boolean} 是否有效
 */
export function isValidPhone(phone) {
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

/**
 * 验证身份证号格式
 * @param {string} idCard - 身份证号
 * @returns {boolean} 是否有效
 */
export function isValidIdCard(idCard) {
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  return idCardRegex.test(idCard)
}

/**
 * 获取URL参数
 * @param {string} name - 参数名
 * @param {string} url - URL，默认当前页面URL
 * @returns {string|null} 参数值
 */
export function getUrlParam(name, url = window.location.href) {
  const regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)')
  const results = regex.exec(url)
  if (!results) return null
  if (!results[2]) return ''
  return decodeURIComponent(results[2].replace(/\+/g, ' '))
}

/**
 * 设置URL参数
 * @param {string} name - 参数名
 * @param {string} value - 参数值
 * @param {string} url - URL，默认当前页面URL
 * @returns {string} 新的URL
 */
export function setUrlParam(name, value, url = window.location.href) {
  const regex = new RegExp('([?&])' + name + '=.*?(&|$)', 'i')
  const separator = url.indexOf('?') !== -1 ? '&' : '?'
  
  if (url.match(regex)) {
    return url.replace(regex, '$1' + name + '=' + value + '$2')
  } else {
    return url + separator + name + '=' + value
  }
}

/**
 * 本地存储封装
 */
export const storage = {
  /**
   * 设置存储
   * @param {string} key - 键
   * @param {any} value - 值
   * @param {number} expire - 过期时间（秒），默认不过期
   */
  set(key, value, expire = null) {
    const data = {
      value,
      expire: expire ? Date.now() + expire * 1000 : null
    }
    uni.setStorageSync(key, JSON.stringify(data))
  },
  
  /**
   * 获取存储
   * @param {string} key - 键
   * @param {any} defaultValue - 默认值
   * @returns {any} 值
   */
  get(key, defaultValue = null) {
    try {
      const data = uni.getStorageSync(key)
      if (!data) return defaultValue
      
      const parsed = JSON.parse(data)
      
      // 检查是否过期
      if (parsed.expire && Date.now() > parsed.expire) {
        uni.removeStorageSync(key)
        return defaultValue
      }
      
      return parsed.value
    } catch (error) {
      console.error('Storage get error:', error)
      return defaultValue
    }
  },
  
  /**
   * 删除存储
   * @param {string} key - 键
   */
  remove(key) {
    uni.removeStorageSync(key)
  },
  
  /**
   * 清空存储
   */
  clear() {
    uni.clearStorageSync()
  }
}

/**
 * 状态管理
 */
export const statusMap = {
  house: {
    pending: { text: '待审核', color: '#E6A23C' },
    approved: { text: '已通过', color: '#67C23A' },
    rejected: { text: '已驳回', color: '#F56C6C' }
  },
  user: {
    active: { text: '正常', color: '#67C23A' },
    banned: { text: '已封禁', color: '#F56C6C' }
  }
}

/**
 * 获取状态信息
 * @param {string} type - 类型
 * @param {string} status - 状态
 * @returns {object} 状态信息
 */
export function getStatusInfo(type, status) {
  return statusMap[type]?.[status] || { text: status, color: '#909399' }
}

/**
 * 图片处理
 */
export const imageUtils = {
  /**
   * 压缩图片
   * @param {string} src - 图片路径
   * @param {number} quality - 质量，0-1
   * @param {number} maxWidth - 最大宽度
   * @returns {Promise<string>} 压缩后的base64
   */
  compress(src, quality = 0.8, maxWidth = 800) {
    return new Promise((resolve, reject) => {
      uni.getImageInfo({
        src,
        success: (res) => {
          const canvas = uni.createCanvasContext('imageCanvas')
          const { width, height } = res
          
          let newWidth = width
          let newHeight = height
          
          if (width > maxWidth) {
            newWidth = maxWidth
            newHeight = (height * maxWidth) / width
          }
          
          canvas.drawImage(src, 0, 0, newWidth, newHeight)
          canvas.draw(false, () => {
            uni.canvasToTempFilePath({
              canvasId: 'imageCanvas',
              quality,
              success: (result) => resolve(result.tempFilePath),
              fail: reject
            })
          })
        },
        fail: reject
      })
    })
  },
  
  /**
   * 获取图片信息
   * @param {string} src - 图片路径
   * @returns {Promise<object>} 图片信息
   */
  getInfo(src) {
    return new Promise((resolve, reject) => {
      uni.getImageInfo({
        src,
        success: resolve,
        fail: reject
      })
    })
  }
}
