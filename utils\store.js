/**
 * 简单的状态管理
 * 用于管理全局状态和数据缓存
 */

/**
 * 状态管理类
 */
class Store {
  constructor() {
    this.state = {
      // 用户信息
      user: {
        isLoggedIn: false,
        userInfo: null,
        token: null
      },
      
      // 系统配置
      config: {
        announcement: '',
        contact_info: {},
        banner_list: [],
        search_keywords: []
      },
      
      // 统计数据
      statistics: {
        users: {},
        houses: {},
        favorites: {}
      },
      
      // 缓存数据
      cache: {
        houseList: [],
        userList: [],
        lastUpdateTime: {}
      },
      
      // 应用状态
      app: {
        loading: false,
        networkStatus: 'online'
      }
    }
    
    // 订阅者列表
    this.subscribers = {}
    
    // 初始化
    this.init()
  }

  /**
   * 初始化状态
   */
  init() {
    // 从本地存储恢复用户状态
    this.restoreUserState()
    
    // 监听网络状态
    this.watchNetworkStatus()
  }

  /**
   * 恢复用户状态
   */
  restoreUserState() {
    try {
      const token = uni.getStorageSync('admin_token')
      const userInfo = uni.getStorageSync('admin_user')
      
      if (token && userInfo) {
        this.setState('user', {
          isLoggedIn: true,
          userInfo,
          token
        })
      }
    } catch (error) {
      console.error('恢复用户状态失败:', error)
    }
  }

  /**
   * 监听网络状态
   */
  watchNetworkStatus() {
    uni.onNetworkStatusChange((res) => {
      this.setState('app.networkStatus', res.isConnected ? 'online' : 'offline')
    })
  }

  /**
   * 设置状态
   * @param {string} path - 状态路径，支持点分割
   * @param {any} value - 状态值
   */
  setState(path, value) {
    const keys = path.split('.')
    let current = this.state
    
    // 导航到目标位置
    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i]
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {}
      }
      current = current[key]
    }
    
    // 设置值
    const lastKey = keys[keys.length - 1]
    const oldValue = current[lastKey]
    current[lastKey] = value
    
    // 通知订阅者
    this.notify(path, value, oldValue)
  }

  /**
   * 获取状态
   * @param {string} path - 状态路径，支持点分割
   * @returns {any} 状态值
   */
  getState(path) {
    const keys = path.split('.')
    let current = this.state
    
    for (const key of keys) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key]
      } else {
        return undefined
      }
    }
    
    return current
  }

  /**
   * 订阅状态变化
   * @param {string} path - 状态路径
   * @param {Function} callback - 回调函数
   * @returns {Function} 取消订阅函数
   */
  subscribe(path, callback) {
    if (!this.subscribers[path]) {
      this.subscribers[path] = []
    }
    
    this.subscribers[path].push(callback)
    
    // 返回取消订阅函数
    return () => {
      const index = this.subscribers[path].indexOf(callback)
      if (index > -1) {
        this.subscribers[path].splice(index, 1)
      }
    }
  }

  /**
   * 通知订阅者
   * @param {string} path - 状态路径
   * @param {any} newValue - 新值
   * @param {any} oldValue - 旧值
   */
  notify(path, newValue, oldValue) {
    // 通知精确路径的订阅者
    if (this.subscribers[path]) {
      this.subscribers[path].forEach(callback => {
        try {
          callback(newValue, oldValue, path)
        } catch (error) {
          console.error('状态订阅回调执行失败:', error)
        }
      })
    }
    
    // 通知父路径的订阅者
    const pathParts = path.split('.')
    for (let i = pathParts.length - 1; i > 0; i--) {
      const parentPath = pathParts.slice(0, i).join('.')
      if (this.subscribers[parentPath]) {
        this.subscribers[parentPath].forEach(callback => {
          try {
            callback(this.getState(parentPath), undefined, parentPath)
          } catch (error) {
            console.error('状态订阅回调执行失败:', error)
          }
        })
      }
    }
  }

  /**
   * 用户登录
   * @param {object} userInfo - 用户信息
   * @param {string} token - 访问令牌
   */
  login(userInfo, token) {
    // 保存到本地存储
    uni.setStorageSync('admin_token', token)
    uni.setStorageSync('admin_user', userInfo)
    
    // 更新状态
    this.setState('user', {
      isLoggedIn: true,
      userInfo,
      token
    })
  }

  /**
   * 用户登出
   */
  logout() {
    // 清除本地存储
    uni.removeStorageSync('admin_token')
    uni.removeStorageSync('admin_user')
    
    // 重置用户状态
    this.setState('user', {
      isLoggedIn: false,
      userInfo: null,
      token: null
    })
    
    // 清除缓存
    this.clearCache()
  }

  /**
   * 更新统计数据
   * @param {object} statistics - 统计数据
   */
  updateStatistics(statistics) {
    this.setState('statistics', statistics)
  }

  /**
   * 更新系统配置
   * @param {object} config - 系统配置
   */
  updateConfig(config) {
    this.setState('config', { ...this.state.config, ...config })
  }

  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {any} data - 缓存数据
   * @param {number} ttl - 生存时间（秒），默认5分钟
   */
  setCache(key, data, ttl = 300) {
    const now = Date.now()
    this.setState(`cache.${key}`, data)
    this.setState(`cache.lastUpdateTime.${key}`, now + ttl * 1000)
  }

  /**
   * 获取缓存
   * @param {string} key - 缓存键
   * @returns {any} 缓存数据，如果过期则返回null
   */
  getCache(key) {
    const data = this.getState(`cache.${key}`)
    const expireTime = this.getState(`cache.lastUpdateTime.${key}`)
    
    if (!data || !expireTime) {
      return null
    }
    
    if (Date.now() > expireTime) {
      // 缓存已过期
      this.setState(`cache.${key}`, null)
      this.setState(`cache.lastUpdateTime.${key}`, null)
      return null
    }
    
    return data
  }

  /**
   * 清除缓存
   * @param {string} key - 缓存键，不传则清除所有缓存
   */
  clearCache(key) {
    if (key) {
      this.setState(`cache.${key}`, null)
      this.setState(`cache.lastUpdateTime.${key}`, null)
    } else {
      this.setState('cache', {
        houseList: [],
        userList: [],
        lastUpdateTime: {}
      })
    }
  }

  /**
   * 设置加载状态
   * @param {boolean} loading - 是否加载中
   */
  setLoading(loading) {
    this.setState('app.loading', loading)
  }

  /**
   * 获取网络状态
   * @returns {string} 网络状态：online 或 offline
   */
  getNetworkStatus() {
    return this.getState('app.networkStatus')
  }

  /**
   * 检查是否已登录
   * @returns {boolean} 是否已登录
   */
  isLoggedIn() {
    return this.getState('user.isLoggedIn')
  }

  /**
   * 获取当前用户信息
   * @returns {object|null} 用户信息
   */
  getCurrentUser() {
    return this.getState('user.userInfo')
  }

  /**
   * 获取当前访问令牌
   * @returns {string|null} 访问令牌
   */
  getToken() {
    return this.getState('user.token')
  }

  /**
   * 重置状态
   */
  reset() {
    this.state = {
      user: {
        isLoggedIn: false,
        userInfo: null,
        token: null
      },
      config: {
        announcement: '',
        contact_info: {},
        banner_list: [],
        search_keywords: []
      },
      statistics: {
        users: {},
        houses: {},
        favorites: {}
      },
      cache: {
        houseList: [],
        userList: [],
        lastUpdateTime: {}
      },
      app: {
        loading: false,
        networkStatus: 'online'
      }
    }
    
    // 通知所有订阅者
    Object.keys(this.subscribers).forEach(path => {
      this.notify(path, this.getState(path), undefined)
    })
  }
}

// 创建全局状态管理实例
const store = new Store()

// 导出状态管理实例
export default store

// 导出便捷方法
export const {
  setState,
  getState,
  subscribe,
  login,
  logout,
  updateStatistics,
  updateConfig,
  setCache,
  getCache,
  clearCache,
  setLoading,
  getNetworkStatus,
  isLoggedIn,
  getCurrentUser,
  getToken
} = store
